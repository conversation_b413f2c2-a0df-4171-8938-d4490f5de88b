import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import '../../../models/invoice.dart';
import '../../services/receipt_builder.dart';
import 'print_preview_page.dart';

class PrintSettingsPage extends StatefulWidget {
  final Invoice? invoice;
  final String? pdfPath;

  const PrintSettingsPage.fromInvoice({super.key, required this.invoice})
    : pdfPath = null;

  const PrintSettingsPage.fromPdf({super.key, required this.pdfPath})
    : invoice = null;

  @override
  State<PrintSettingsPage> createState() => _PrintSettingsPageState();
}

class _PrintSettingsPageState extends State<PrintSettingsPage> {
  late ReceiptConfig _config;
  String _selectedPaperSize = '3*5inch';
  String _selectedPrintEffect = 'Original';
  String _selectedWatermark = 'None';
  bool _smartCrop = true;

  @override
  void initState() {
    super.initState();
    _config = ReceiptConfig();
  }

  Widget _buildMiniInvoicePreview() {
    return Container(
      width: double.infinity,
      height: 400,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // Header avec dimensions
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'PDF print',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    const Text('78mm', style: TextStyle(fontSize: 12)),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.rotate_right, size: 16),
                      onPressed: () {},
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Contenu de la mini facture
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child:
                  widget.pdfPath != null
                      ? _buildPdfPreview()
                      : _buildInvoiceContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceContent() {
    final invoice = widget.invoice;
    return Column(
      children: [
        // Référence
        Text(
          'Ref: ${invoice != null ? 'F${invoice.id.toString().padLeft(6, '0')}' : 'EXEMPLE-001'}',
          style: const TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        // Logo et QR codes
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
              ),
              child: const Icon(Icons.qr_code, size: 30),
            ),
            const Text(
              'HCP\nDESIGN',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
              ),
              child: const Icon(Icons.qr_code, size: 30),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Informations client
        Column(
          children: [
            const Text(
              'Nom du client',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              invoice?.clientName ?? 'Client Exemple',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            const Text(
              'Numéro du client',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              invoice?.clientNumber ?? '+225 0123456789',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            const Text(
              'Lieu de livraison',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              invoice?.deliveryLocation ?? 'Abidjan',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            if (invoice?.deliveryDetails != null) ...[
              const Text(
                'Détails livraison',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
              Text(
                invoice!.deliveryDetails!,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
            ],

            const Text(
              'Date',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              DateTime.now().toString().substring(0, 10),
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            const Text(
              'Reste à payer',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              '${invoice?.total.toStringAsFixed(0) ?? '15000'} FCFA',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),

        const Spacer(),

        // QR de paiement
        const Text(
          'QR de paiement',
          style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Column(
              children: [
                const Text('Wave', style: TextStyle(fontSize: 10)),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black),
                  ),
                  child: const Icon(Icons.qr_code, size: 20),
                ),
              ],
            ),
            Column(
              children: [
                const Text('Orange', style: TextStyle(fontSize: 10)),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black),
                  ),
                  child: const Icon(Icons.qr_code, size: 20),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPdfPreview() {
    if (widget.pdfPath == null || !File(widget.pdfPath!).existsSync()) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red),
            SizedBox(height: 8),
            Text('PDF non trouvé', style: TextStyle(color: Colors.red)),
          ],
        ),
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: PDFView(
        filePath: widget.pdfPath!,
        enableSwipe: false,
        swipeHorizontal: false,
        autoSpacing: false,
        pageFling: false,
        pageSnap: false,
        fitPolicy: FitPolicy.BOTH,
        onError: (error) {
          debugPrint('Erreur PDF: $error');
        },
        onPageError: (page, error) {
          debugPrint('Erreur page PDF $page: $error');
        },
      ),
    );
  }

  Widget _buildPaperSizeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Paper size',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            TextButton(
              onPressed: () {},
              child: const Text(
                'Setting',
                style: TextStyle(color: Colors.blue),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildPaperSizeOption(
              '3*4inch',
              '76*100mm',
              _selectedPaperSize == '3*4inch',
            ),
            _buildPaperSizeOption(
              '3*5inch',
              '76*130mm',
              _selectedPaperSize == '3*5inch',
            ),
            _buildPaperSizeOption(
              '3*5inch',
              '78*120mm',
              _selectedPaperSize == '78*120mm',
            ),
            _buildPaperSizeOption(
              '4*4inch',
              '100*100mm',
              _selectedPaperSize == '4*4inch',
            ),
            _buildPaperSizeOption(
              '4*6inch',
              '100*150mm',
              _selectedPaperSize == '4*6inch',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaperSizeOption(
    String size,
    String dimensions,
    bool isSelected,
  ) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPaperSize = size;
        });
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.grey[300]!,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: isSelected ? Colors.blue[50] : Colors.white,
            ),
            child: Center(
              child: Icon(
                Icons.rectangle_outlined,
                color: isSelected ? Colors.blue : Colors.grey[600],
                size: 30,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            size,
            style: TextStyle(
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? Colors.blue : Colors.black,
            ),
          ),
          Text(
            dimensions,
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildPrintEffectSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Print effect',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildPrintEffectOption(
              'Original',
              _selectedPrintEffect == 'Original',
            ),
            _buildPrintEffectOption(
              'Black white',
              _selectedPrintEffect == 'Black white',
            ),
            _buildPrintEffectOption('Dither', _selectedPrintEffect == 'Dither'),
          ],
        ),
      ],
    );
  }

  Widget _buildPrintEffectOption(String effect, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPrintEffect = effect;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
          ),
          borderRadius: BorderRadius.circular(20),
          color: isSelected ? Colors.blue[50] : Colors.white,
        ),
        child: Text(
          effect,
          style: TextStyle(
            color: isSelected ? Colors.blue : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildWatermarkSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Watermark settings',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildWatermarkOption('None', _selectedWatermark == 'None'),
            _buildWatermarkOption('Image', _selectedWatermark == 'Image'),
            _buildWatermarkOption('Word', _selectedWatermark == 'Word'),
          ],
        ),
      ],
    );
  }

  Widget _buildWatermarkOption(String watermark, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedWatermark = watermark;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
          ),
          borderRadius: BorderRadius.circular(20),
          color: isSelected ? Colors.blue[50] : Colors.white,
        ),
        child: Text(
          watermark,
          style: TextStyle(
            color: isSelected ? Colors.blue : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildSmartCropSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Smart crop',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        Switch(
          value: _smartCrop,
          onChanged: (value) {
            setState(() {
              _smartCrop = value;
            });
          },
          activeColor: Colors.blue,
        ),
      ],
    );
  }

  void _navigateToPreview() {
    if (widget.invoice != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => PrintPreviewPage.fromInvoice(
                invoice: widget.invoice!,
                config: _config,
              ),
        ),
      );
    } else if (widget.pdfPath != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => PrintPreviewPage.fromPdf(
                pdfPath: widget.pdfPath!,
                config: _config,
              ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('PDF print'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: () {},
            child: const Text('Rotate', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Preview de la mini facture
                  _buildMiniInvoicePreview(),

                  const SizedBox(height: 24),

                  // Paper size section
                  _buildPaperSizeSection(),

                  const SizedBox(height: 24),

                  // Print effect section
                  _buildPrintEffectSection(),

                  const SizedBox(height: 24),

                  // Watermark settings section
                  _buildWatermarkSection(),

                  const SizedBox(height: 24),

                  // Smart crop section
                  _buildSmartCropSection(),
                ],
              ),
            ),
          ),

          // Next button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: _navigateToPreview,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Next',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
