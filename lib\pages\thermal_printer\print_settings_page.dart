import 'package:flutter/material.dart';
import '../../../models/invoice.dart';
import '../../services/receipt_builder.dart';
import 'print_preview_page.dart';

class PrintSettingsPage extends StatefulWidget {
  final Invoice? invoice;
  final String? pdfPath;

  const PrintSettingsPage.fromInvoice({
    super.key,
    required this.invoice,
  }) : pdfPath = null;

  const PrintSettingsPage.fromPdf({
    super.key,
    required this.pdfPath,
  }) : invoice = null;

  @override
  State<PrintSettingsPage> createState() => _PrintSettingsPageState();
}

class _PrintSettingsPageState extends State<PrintSettingsPage> {
  late ReceiptConfig _config;
  String _previewText = '';

  @override
  void initState() {
    super.initState();
    _config = ReceiptConfig();
    _generatePreview();
  }

  void _generatePreview() {
    setState(() {
      _previewText = _generatePreviewText();
    });
  }

  String _generatePreviewText() {
    return '''
╔══════════════════════════════════════╗
║            MINI FACTURE              ║
╠══════════════════════════════════════╣
║                                      ║
║  ENTREPRISE ABC                      ║
║  123 Rue de la Paix                  ║
║  75001 Paris, France                 ║
║  Tél: +33 1 23 45 67 89              ║
║  Email: <EMAIL>    ║
║                                      ║
╠══════════════════════════════════════╣
║                                      ║
║  FACTURE N°: FAC-2024-001           ║
║  Date: 15/01/2024                    ║
║  Échéance: 15/02/2024                ║
║                                      ║
║  CLIENT:                             ║
║  Jean Dupont                         ║
║  456 Avenue des Champs               ║
║  75008 Paris                         ║
║                                      ║
╠══════════════════════════════════════╣
║                                      ║
║  ARTICLES:                           ║
║                                      ║
║  1x Service Consultation             ║
║     Prix unitaire: 150,00 €          ║
║     Total: 150,00 €                  ║
║                                      ║
║  2x Formation                        ║
║     Prix unitaire: 75,00 €           ║
║     Total: 150,00 €                  ║
║                                      ║
║  ────────────────────────────────    ║
║  Sous-total HT: 300,00 €             ║
║  TVA (20%): 60,00 €                  ║
║  TOTAL TTC: 360,00 €                 ║
║                                      ║
╠══════════════════════════════════════╣
║                                      ║
║  NOTES:                              ║
║  Paiement par virement bancaire      ║
║  IBAN: FR76 1234 5678 9012 3456     ║
║                                      ║
║  Merci pour votre confiance !        ║
║                                      ║
║  [QR CODE]                           ║
║                                      ║
╚══════════════════════════════════════╝
''';
  }

  void _navigateToPreview() {
    if (widget.invoice != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrintPreviewPage.fromInvoice(
            invoice: widget.invoice!,
            config: _config,
          ),
        ),
      );
    } else if (widget.pdfPath != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrintPreviewPage.fromPdf(
            pdfPath: widget.pdfPath!,
            config: _config,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres d\'impression'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Preview section
                  _buildSettingCard(
                    title: 'Prévisualisation Mini Facture',
                    icon: Icons.preview,
                    child: Container(
                      width: double.infinity,
                      height: 300,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          _previewText,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 10,
                            height: 1.2,
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Configurable options
                  _buildSettingCard(
                    title: 'Options configurables',
                    icon: Icons.settings,
                    child: Column(
                      children: [
                        // Paper size
                         DropdownButtonFormField<String>(
                           value: _config.paperSize,
                           decoration: const InputDecoration(
                             labelText: 'Paper size',
                             border: OutlineInputBorder(),
                           ),
                           items: PaperSizeConfig.displayNames.entries.map((entry) {
                             return DropdownMenuItem(
                               value: entry.key,
                               child: Text(entry.value),
                             );
                           }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _config = _config.copyWith(paperSize: value);
                                _generatePreview();
                              });
                            }
                          },
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Print effect
                        DropdownButtonFormField<PrintEffect>(
                          value: _config.printEffect,
                          decoration: const InputDecoration(
                            labelText: 'Print effect',
                            border: OutlineInputBorder(),
                          ),
                          items: PrintEffect.values.map((effect) {
                            return DropdownMenuItem(
                              value: effect,
                              child: Text(effect.displayName),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _config = _config.copyWith(printEffect: value);
                                _generatePreview();
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Watermark settings
                  _buildSettingCard(
                    title: 'Watermark settings',
                    icon: Icons.water_drop,
                    child: Column(
                      children: [
                        DropdownButtonFormField<WatermarkType>(
                          value: _config.watermark.type,
                          decoration: const InputDecoration(
                            labelText: 'Watermark type',
                            border: OutlineInputBorder(),
                          ),
                          items: WatermarkType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Text(type.displayName),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _config = _config.copyWith(
                                  watermark: _config.watermark.copyWith(type: value),
                                );
                                _generatePreview();
                              });
                            }
                          },
                        ),
                        
                        if (_config.watermark.type == WatermarkType.text) ...[
                          const SizedBox(height: 16),
                          TextFormField(
                            initialValue: _config.watermark.text,
                            decoration: const InputDecoration(
                              labelText: 'Watermark text',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) {
                              setState(() {
                                _config = _config.copyWith(
                                  watermark: _config.watermark.copyWith(text: value),
                                );
                                _generatePreview();
                              });
                            },
                          ),
                        ],
                        
                        const SizedBox(height: 16),
                        
                        Row(
                          children: [
                            const Text('Opacity: '),
                            Expanded(
                              child: Slider(
                                value: _config.watermark.opacity,
                                min: 0.0,
                                max: 1.0,
                                divisions: 10,
                                label: '${(_config.watermark.opacity * 100).round()}%',
                                onChanged: (value) {
                                  setState(() {
                                    _config = _config.copyWith(
                                      watermark: _config.watermark.copyWith(opacity: value),
                                    );
                                    _generatePreview();
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Smart crop
                  _buildSettingCard(
                    title: 'Smart crop',
                    icon: Icons.crop,
                    child: SwitchListTile(
                      value: _config.smartCrop,
                      onChanged: (value) {
                        setState(() {
                          _config = _config.copyWith(smartCrop: value);
                          _generatePreview();
                        });
                      },
                      title: Text(
                        _config.smartCrop ? 'Activé' : 'Désactivé',
                        style: const TextStyle(fontSize: 14),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Next button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: ElevatedButton.icon(
              onPressed: _navigateToPreview,
              icon: const Icon(Icons.arrow_forward),
              label: const Text('Next'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Colors.blue[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            child,
          ],
        ),
      ),
    );
  }
}