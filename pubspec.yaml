name: general_hcp_crm
description: "General HCP CRM - G-n-ral-HCP-CRM"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.1+3

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  provider: ^6.1.2

  # Dependencies for HCP-DESIGN Invoice Generator
  shared_preferences: ^2.2.2
  pdf: ^3.10.7
  path_provider: ^2.1.2
  intl: ^0.19.0
  uuid: ^4.3.3
  # Remplacement de printing par des alternatives plus compatibles
  # printing: ^5.13.1
  flutter_pdfview: ^1.3.2
  open_file: ^3.3.2
  file_picker: ^8.0.0+1
  image_picker: ^1.0.7 # Added image_picker dependency
  csv: ^5.1.1
  excel: ^4.0.3 # Added excel dependency for Excel import/export
  flutter_local_notifications: ^17.2.2 # Added for local notifications
  permission_handler: ^11.3.1 # Added for notification permissions
  http_parser: ^4.0.2 # Added for media upload
  path: ^1.8.3 # Added for file path operations
  workmanager: ^0.7.0 # Added for background tasks
  network_info_plus: ^6.1.4 # Added for network information
  http: ^1.2.2 # Added for HTTP requests
  package_info_plus: ^8.0.2 # Added for app version information
  share_plus: ^7.2.2 # Added for backup sharing functionality
  local_auth: ^2.1.8 # Added for biometric authentication
  record: ^6.0.0 # Added for audio recording
  audioplayers: ^6.0.0 # Added for playing notification sounds
  url_launcher: ^6.2.5 # Added for WhatsApp links
  font_awesome_flutter: ^10.7.0 # Added for WhatsApp logo

  # Thermal printing dependencies
  print_bluetooth_thermal: ^1.1.2
  esc_pos_utils_plus: ^2.0.2
  image: ^4.0.17

  # Firebase dependencies
  firebase_core: ^2.27.1
  firebase_database: ^10.4.10
  firebase_auth: ^4.17.9
  firebase_storage: ^11.6.10
  connectivity_plus: ^5.0.2 # For checking internet connectivity

  # Supabase dependencies
  supabase_flutter: ^2.5.6

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

# Dependency overrides to fix platform-specific plugin issues
dependency_overrides:
  # Override to fix open_file_macos plugin missing warning on Android
  open_file_macos: 0.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/navigation/
    - assets/qr_codes/QR_Site/
    - assets/qr_codes/QR_WhatsApp/
    - assets/qr_codes/QR_Coques/
    - assets/qr_codes/QR_Paiement_Wave/
    - assets/qr_codes/QR_Paiement_Orange/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Configuration pour les icônes de lancement
flutter_launcher_icons:
  android: true
  ios: true
  web:
    generate: true
  windows:
    generate: true
  image_path: "assets/icon/app_icon.png"
  min_sdk_android: 21
