import 'package:flutter/material.dart';
import '../models/advanced_task.dart';
import '../models/task_user.dart';
import '../services/advanced_task_service.dart';
import '../widgets/advanced_task_widgets.dart';
import '../widgets/task_dialogs.dart';
import '../widgets/kanban_view.dart';
import '../widgets/calendar_view.dart';
import '../widgets/timeline_view.dart';
import 'task_detail_page.dart';

class AdvancedTasksPage extends StatefulWidget {
  const AdvancedTasksPage({super.key});

  @override
  State<AdvancedTasksPage> createState() => _AdvancedTasksPageState();
}

class _AdvancedTasksPageState extends State<AdvancedTasksPage>
    with TickerProviderStateMixin {
  final AdvancedTaskService _taskService = AdvancedTaskService();

  late TabController _tabController;
  TaskFilter _currentFilter = TaskFilter();
  TaskSortOption _currentSort = TaskSortOption.dueDate;

  List<Project> _projects = [];
  List<AdvancedTask> _tasks = [];

  bool _isLoading = true;
  String? _selectedProjectId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      await _taskService.initialize();

      final projects = await _taskService.getProjects();
      final tasks = await _taskService.getTasks(filter: _currentFilter);

      setState(() {
        _projects = projects;
        _tasks = tasks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Tâches'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.list), text: 'Liste'),
            Tab(icon: Icon(Icons.view_kanban), text: 'Kanban'),
            Tab(icon: Icon(Icons.calendar_month), text: 'Calendrier'),
            Tab(icon: Icon(Icons.timeline), text: 'Timeline'),
          ],
        ),
        actions: [
          // Sélecteur de projet
          if (_projects.isNotEmpty)
            PopupMenuButton<String>(
              icon: const Icon(Icons.folder),
              tooltip: 'Sélectionner un projet',
              onSelected: (projectId) {
                setState(() {
                  _selectedProjectId = projectId == 'all' ? null : projectId;
                  _currentFilter = _currentFilter.copyWith(
                    projectId: _selectedProjectId,
                  );
                });
                _loadData();
              },
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'all',
                      child: Row(
                        children: [
                          Icon(Icons.all_inclusive),
                          SizedBox(width: 8),
                          Text('Tous les projets'),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    ..._projects.map(
                      (project) => PopupMenuItem(
                        value: project.id,
                        child: Row(
                          children: [
                            Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: project.color,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(project.name)),
                          ],
                        ),
                      ),
                    ),
                  ],
            ),

          // Filtres
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),

          // Tri
          PopupMenuButton<TaskSortOption>(
            icon: const Icon(Icons.sort),
            onSelected: (sortOption) {
              setState(() => _currentSort = sortOption);
              _applySorting();
            },
            itemBuilder:
                (context) =>
                    TaskSortOption.values
                        .map(
                          (option) => PopupMenuItem(
                            value: option,
                            child: Row(
                              children: [
                                Icon(_getSortIcon(option)),
                                const SizedBox(width: 8),
                                Text(option.displayName),
                              ],
                            ),
                          ),
                        )
                        .toList(),
          ),

          // Ajouter une tâche
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddTaskDialog,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Barre de statistiques
                  _buildStatsBar(),

                  // Contenu principal
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildListView(),
                        _buildKanbanView(),
                        _buildCalendarView(),
                        _buildTimelineView(),
                      ],
                    ),
                  ),
                ],
              ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddTaskDialog,
        icon: const Icon(Icons.add),
        label: const Text('Nouvelle tâche'),
      ),
    );
  }

  Widget _buildStatsBar() {
    final totalTasks = _tasks.length;
    final completedTasks = _tasks.where((t) => t.isCompleted).length;
    final overdueTasks = _tasks.where((t) => t.isOverdue).length;
    final todayTasks =
        _tasks
            .where(
              (t) =>
                  t.dueDate != null &&
                  DateUtils.isSameDay(t.dueDate!, DateTime.now()),
            )
            .length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'Total',
            totalTasks.toString(),
            Icons.task_alt,
            Colors.blue,
          ),
          _buildStatItem(
            'Terminées',
            completedTasks.toString(),
            Icons.check_circle,
            Colors.green,
          ),
          _buildStatItem(
            'En retard',
            overdueTasks.toString(),
            Icons.warning,
            Colors.red,
          ),
          _buildStatItem(
            'Aujourd\'hui',
            todayTasks.toString(),
            Icons.today,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildListView() {
    if (_tasks.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.task_alt, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Aucune tâche trouvée',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Créez votre première tâche pour commencer',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _tasks.length,
      itemBuilder: (context, index) {
        final task = _tasks[index];
        return TaskListTile(
          task: task,
          onTap: () => _showTaskDetails(task),
          onStatusChanged: (newStatus) => _updateTaskStatus(task, newStatus),
        );
      },
    );
  }

  Widget _buildKanbanView() {
    return KanbanView(
      tasks: _tasks,
      onTaskTap: _showTaskDetails,
      onStatusChanged: _updateTaskStatus,
    );
  }

  Widget _buildCalendarView() {
    return CalendarView(tasks: _tasks, onTaskTap: _showTaskDetails);
  }

  Widget _buildTimelineView() {
    return TimelineView(tasks: _tasks, onTaskTap: _showTaskDetails);
  }

  IconData _getSortIcon(TaskSortOption option) {
    switch (option) {
      case TaskSortOption.dueDate:
        return Icons.schedule;
      case TaskSortOption.priority:
        return Icons.priority_high;
      case TaskSortOption.status:
        return Icons.flag;
      case TaskSortOption.assignee:
        return Icons.person;
      case TaskSortOption.createdDate:
        return Icons.access_time;
      case TaskSortOption.title:
        return Icons.title;
      case TaskSortOption.project:
        return Icons.folder;
    }
  }

  void _applySorting() {
    setState(() {
      _tasks.sort((a, b) {
        switch (_currentSort) {
          case TaskSortOption.dueDate:
            if (a.dueDate == null && b.dueDate == null) return 0;
            if (a.dueDate == null) return 1;
            if (b.dueDate == null) return -1;
            return a.dueDate!.compareTo(b.dueDate!);
          case TaskSortOption.priority:
            return b.priority.index.compareTo(a.priority.index);
          case TaskSortOption.status:
            return a.status.index.compareTo(b.status.index);
          case TaskSortOption.title:
            return a.title.compareTo(b.title);
          case TaskSortOption.createdDate:
            return b.createdAt.compareTo(a.createdAt);
          default:
            return 0;
        }
      });
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => FilterDialog(
            currentFilter: _currentFilter,
            onFilterChanged: (filter) {
              setState(() {
                _currentFilter = filter;
              });
              _loadData();
            },
          ),
    );
  }

  void _showAddTaskDialog() {
    showDialog(
      context: context,
      builder:
          (context) => TaskDialog(
            projectId: _selectedProjectId,
            onTaskSaved: (task) async {
              final messenger = ScaffoldMessenger.of(context);
              try {
                await _taskService.addTask(task);
                _loadData();
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Tâche "${task.title}" créée avec succès'),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Erreur lors de la création: $e')),
                  );
                }
              }
            },
          ),
    );
  }

  void _showTaskDetails(AdvancedTask task) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => TaskDetailPage(task: task)),
    ).then((_) => _loadData()); // Recharger les données au retour
  }

  void _updateTaskStatus(AdvancedTask task, TaskStatus newStatus) async {
    try {
      final updatedTask = task.copyWith(status: newStatus);
      await _taskService.updateTask(updatedTask);
      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la mise à jour: $e')),
        );
      }
    }
  }
}
