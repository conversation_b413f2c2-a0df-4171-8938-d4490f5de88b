import 'package:flutter/material.dart';
import '../models/advanced_task.dart';
import '../services/advanced_task_service.dart';
import '../widgets/advanced_task_widgets.dart';

class ProjectsPage extends StatefulWidget {
  const ProjectsPage({super.key});

  @override
  State<ProjectsPage> createState() => _ProjectsPageState();
}

class _ProjectsPageState extends State<ProjectsPage> {
  final AdvancedTaskService _taskService = AdvancedTaskService();
  
  List<Project> _projects = [];
  Map<String, int> _taskCounts = {};
  Map<String, int> _completedCounts = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    setState(() => _isLoading = true);
    
    try {
      await _taskService.initialize();
      
      final projects = await _taskService.getProjects();
      final taskCounts = <String, int>{};
      final completedCounts = <String, int>{};
      
      for (final project in projects) {
        final tasks = await _taskService.getTasksByProject(project.id);
        taskCounts[project.id] = tasks.length;
        completedCounts[project.id] = tasks.where((t) => t.isCompleted).length;
      }
      
      setState(() {
        _projects = projects;
        _taskCounts = taskCounts;
        _completedCounts = completedCounts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Projets'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddProjectDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _projects.isEmpty
              ? _buildEmptyState()
              : _buildProjectsList(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddProjectDialog,
        icon: const Icon(Icons.add),
        label: const Text('Nouveau projet'),
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.folder_open, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Aucun projet trouvé',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          SizedBox(height: 8),
          Text(
            'Créez votre premier projet pour organiser vos tâches',
            style: TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProjectsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _projects.length,
      itemBuilder: (context, index) {
        final project = _projects[index];
        final taskCount = _taskCounts[project.id] ?? 0;
        final completedCount = _completedCounts[project.id] ?? 0;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ProjectCard(
            project: project,
            taskCount: taskCount,
            completedCount: completedCount,
            onTap: () => _openProject(project),
          ),
        );
      },
    );
  }

  void _showAddProjectDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    Color selectedColor = Colors.blue;
    ProjectStatus selectedStatus = ProjectStatus.active;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Nouveau projet'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nom du projet',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Text('Couleur: '),
                    const SizedBox(width: 8),
                    ...Colors.primaries.take(6).map((color) => 
                      GestureDetector(
                        onTap: () => setDialogState(() => selectedColor = color),
                        child: Container(
                          width: 32,
                          height: 32,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                            border: selectedColor == color
                                ? Border.all(color: Colors.black, width: 2)
                                : null,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<ProjectStatus>(
                  value: selectedStatus,
                  decoration: const InputDecoration(
                    labelText: 'Statut',
                    border: OutlineInputBorder(),
                  ),
                  items: ProjectStatus.values.map((status) =>
                    DropdownMenuItem(
                      value: status,
                      child: Text(status.displayName),
                    ),
                  ).toList(),
                  onChanged: (status) {
                    if (status != null) {
                      setDialogState(() => selectedStatus = status);
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () => _createProject(
                nameController.text,
                descriptionController.text,
                selectedColor,
                selectedStatus,
              ),
              child: const Text('Créer'),
            ),
          ],
        ),
      ),
    );
  }

  void _createProject(
    String name,
    String description,
    Color color,
    ProjectStatus status,
  ) async {
    if (name.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Le nom du projet est requis')),
      );
      return;
    }

    try {
      final project = Project(
        id: '',
        name: name.trim(),
        description: description.trim(),
        color: color,
        status: status,
        createdAt: DateTime.now(),
        ownerId: 'current_user', // TODO: Récupérer l'utilisateur actuel
      );

      await _taskService.addProject(project);
      Navigator.pop(context);
      _loadProjects();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Projet "${name}" créé avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la création: $e')),
        );
      }
    }
  }

  void _openProject(Project project) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectDetailPage(project: project),
      ),
    ).then((_) => _loadProjects());
  }
}

class ProjectDetailPage extends StatefulWidget {
  final Project project;

  const ProjectDetailPage({super.key, required this.project});

  @override
  State<ProjectDetailPage> createState() => _ProjectDetailPageState();
}

class _ProjectDetailPageState extends State<ProjectDetailPage> {
  final AdvancedTaskService _taskService = AdvancedTaskService();
  
  List<TaskSection> _sections = [];
  List<AdvancedTask> _tasks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjectData();
  }

  Future<void> _loadProjectData() async {
    setState(() => _isLoading = true);
    
    try {
      final sections = await _taskService.getSectionsByProject(widget.project.id);
      final tasks = await _taskService.getTasksByProject(widget.project.id);
      
      setState(() {
        _sections = sections;
        _tasks = tasks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.project.name),
        backgroundColor: widget.project.color,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddSectionDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildProjectHeader(),
                Expanded(child: _buildSectionsList()),
              ],
            ),
    );
  }

  Widget _buildProjectHeader() {
    final totalTasks = _tasks.length;
    final completedTasks = _tasks.where((t) => t.isCompleted).length;
    final progress = totalTasks > 0 ? completedTasks / totalTasks : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.project.color.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.project.description.isNotEmpty) ...[
            Text(
              widget.project.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
          ],
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Progression du projet',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: progress,
                      backgroundColor: Colors.grey.withOpacity(0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(widget.project.color),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Text(
                '$completedTasks/$totalTasks tâches',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionsList() {
    if (_sections.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.view_list, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Aucune section trouvée',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Créez des sections pour organiser vos tâches',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _sections.length,
      itemBuilder: (context, index) {
        final section = _sections[index];
        final sectionTasks = _tasks.where((t) => t.sectionId == section.id).toList();
        
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              section.name,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text('${sectionTasks.length} tâche(s)'),
            children: sectionTasks.map((task) => 
              TaskListTile(
                task: task,
                onTap: () {
                  // TODO: Ouvrir les détails de la tâche
                },
              ),
            ).toList(),
          ),
        );
      },
    );
  }

  void _showAddSectionDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nouvelle section'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la section',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => _createSection(
              nameController.text,
              descriptionController.text,
            ),
            child: const Text('Créer'),
          ),
        ],
      ),
    );
  }

  void _createSection(String name, String description) async {
    if (name.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Le nom de la section est requis')),
      );
      return;
    }

    try {
      final section = TaskSection(
        id: '',
        projectId: widget.project.id,
        name: name.trim(),
        description: description.trim(),
        order: _sections.length,
        createdAt: DateTime.now(),
      );

      await _taskService.addSection(section);
      Navigator.pop(context);
      _loadProjectData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Section "${name}" créée avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la création: $e')),
        );
      }
    }
  }
}
