import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/colis.dart';
import '../services/colis_service.dart';
import '../services/pdf_service.dart';
import '../widgets/password_dialog.dart';
import 'ajouter_livraison_page.dart';
import 'pdf_preview_page.dart';

class CommandesJourPage extends StatefulWidget {
  final DateTime? dateSelectionnee;

  const CommandesJourPage({super.key, this.dateSelectionnee});

  @override
  State<CommandesJourPage> createState() => _CommandesJourPageState();
}

class _CommandesJourPageState extends State<CommandesJourPage> {
  final ColisService _colisService = ColisService.instance;
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  DateTime _dateSelectionnee = DateTime.now();
  List<Colis> _colis = [];
  StatistiquesLivraison? _statistiques;
  bool _isLoading = true;
  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    if (widget.dateSelectionnee != null) {
      _dateSelectionnee = widget.dateSelectionnee!;
    }
    _chargerDonnees();
  }

  /// Méthode helper pour afficher les images de manière compatible web/mobile
  Widget _buildImageWidget(
    String imagePath, {
    BoxFit fit = BoxFit.contain,
    double? width,
    double? height,
  }) {
    if (imagePath.isEmpty) {
      return Container(
        width: width ?? 200,
        height: height ?? 200,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.image_not_supported,
          size: 50,
          color: Colors.grey,
        ),
      );
    }

    // Sur le web, utiliser Image.network ou Image.asset selon le chemin
    if (kIsWeb) {
      if (imagePath.startsWith('http') || imagePath.startsWith('https')) {
        return Image.network(
          imagePath,
          fit: fit,
          width: width,
          height: height,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: width ?? 200,
              height: height ?? 200,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.broken_image,
                size: 50,
                color: Colors.grey,
              ),
            );
          },
        );
      } else {
        // Pour les assets
        return Image.asset(
          imagePath,
          fit: fit,
          width: width,
          height: height,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: width ?? 200,
              height: height ?? 200,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.broken_image,
                size: 50,
                color: Colors.grey,
              ),
            );
          },
        );
      }
    } else {
      // Sur mobile, utiliser Image.file pour les fichiers locaux
      try {
        return Image.file(
          File(imagePath),
          fit: fit,
          width: width,
          height: height,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: width ?? 200,
              height: height ?? 200,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.broken_image,
                size: 50,
                color: Colors.grey,
              ),
            );
          },
        );
      } catch (e) {
        return Container(
          width: width ?? 200,
          height: height ?? 200,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.broken_image,
            size: 50,
            color: Colors.grey,
          ),
        );
      }
    }
  }

  Future<void> _chargerDonnees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final colis = await _colisService.obtenirColisParDate(_dateSelectionnee);
      final stats = StatistiquesLivraison.fromColis(colis, _dateSelectionnee);

      if (mounted) {
        setState(() {
          _colis = colis;
          _statistiques = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  Future<void> _selectionnerDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dateSelectionnee,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null && date != _dateSelectionnee) {
      setState(() {
        _dateSelectionnee = date;
      });
      await _chargerDonnees();
    }
  }

  Future<void> _changerStatutDirectement(
    Colis colis,
    StatutLivraison nouveauStatut,
  ) async {
    try {
      // Créer une copie du colis avec le nouveau statut
      final colisModifie = Colis(
        id: colis.id,
        libelle: colis.libelle,
        photoPath: colis.photoPath,
        zoneLivraison: colis.zoneLivraison,
        numeroClient: colis.numeroClient,
        resteAPayer: colis.resteAPayer,
        fraisLivraison: colis.fraisLivraison,
        dateAjout: colis.dateAjout,
        statut: nouveauStatut,
        nomClient: colis.nomClient,
        adresseLivraison: colis.adresseLivraison,
        notes: colis.notes,
        factureId: colis.factureId,
      );

      // Mettre à jour dans Firebase
      await _colisService.mettreAJourColis(colisModifie);

      // Recharger les données
      await _chargerDonnees();

      // Afficher un message de confirmation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Statut modifié vers "${nouveauStatut.libelle}"'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la modification: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _afficherImagePopup(String imagePath) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Stack(
            children: [
              // Image en plein écran
              Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: _buildImageWidget(
                      imagePath,
                      fit: BoxFit.contain,
                      width: 200,
                      height: 200,
                    ),
                  ),
                ),
              ),
              // Bouton fermer
              Positioned(
                top: 40,
                right: 20,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _naviguerVersModification(Colis colis) async {
    final colisModifie = await Navigator.of(context).push<Colis>(
      MaterialPageRoute(
        builder: (context) => AjouterLivraisonPage(colis: colis),
      ),
    );

    if (colisModifie != null) {
      await _chargerDonnees(); // Recharger les données après modification
    }
  }

  void _supprimerLivraison(Colis colis) async {
    // Vérification du mot de passe avant suppression
    final passwordConfirmed = await PasswordDialog.show(
      context: context,
      title: 'Authentification requise',
      message:
          'Pour supprimer la livraison "${colis.libelle}", veuillez saisir le mot de passe administrateur.',
    );

    if (!passwordConfirmed) return;

    // Confirmation finale de suppression
    if (!mounted) return;
    final bool? confirmer = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer la livraison "${colis.libelle}" ?\n\nCette action est irréversible.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Supprimer'),
            ),
          ],
        );
      },
    );

    if (confirmer == true) {
      try {
        await _colisService.supprimerColis(colis.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Livraison supprimée avec succès'),
              backgroundColor: Colors.green,
            ),
          );
          _chargerDonnees();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la suppression: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _exporterPDF() async {
    if (_statistiques == null || _colis.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Aucune donnée à exporter')));
      return;
    }

    if (kIsWeb) {
      // Sur le web, afficher un message d'information
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('📱 Export PDF disponible sur l\'application mobile'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    setState(() {
      _isExporting = true;
    });

    try {
      final fileName =
          'livraisons_${_dateFormat.format(_dateSelectionnee).replaceAll('/', '_')}.pdf';

      // Générer le PDF pour prévisualisation
      final pdfDocument = await PDFService.generateDeliveryListDocument(
        _colis,
        _dateSelectionnee,
      );

      if (mounted) {
        // Naviguer vers la page de prévisualisation
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => PDFPreviewPage(
                  pdfDocument: pdfDocument,
                  title: 'Prévisualisation Livraisons',
                  fileName: fileName,
                ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur lors de l\'export: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _partagerColisPDF(Colis colis) async {
    if (kIsWeb) {
      // Sur le web, afficher un message d'information
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('📱 Export PDF disponible sur l\'application mobile'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Génération du PDF...'),
              ],
            ),
          );
        },
      );

      // Générer le PDF du colis pour prévisualisation
      final pdfDocument = await PDFService.generateColisDocument(colis);

      // Fermer le dialog de chargement
      if (mounted) {
        Navigator.of(context).pop();

        // Naviguer vers la page de prévisualisation
        final fileName =
            'colis_${colis.numeroClient}_${DateTime.now().millisecondsSinceEpoch}.pdf';

        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => PDFPreviewPage(
                  pdfDocument: pdfDocument,
                  title: 'Prévisualisation Colis',
                  fileName: fileName,
                ),
          ),
        );
      }
    } catch (e) {
      // Fermer le dialog de chargement en cas d'erreur
      if (mounted) {
        Navigator.of(context).pop();

        // Afficher le message d'erreur
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la génération du PDF: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    }
  }

  Future<void> _ajouterLivraison() async {
    // Naviguer vers la page d'ajout de livraison
    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AjouterLivraisonPage()),
    );

    // Recharger les données si une livraison a été ajoutée
    if (result != null) {
      await _chargerDonnees();
    }
  }

  Widget _buildStatistiquesCard() {
    if (_statistiques == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'Statistiques du jour',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Statistiques par statut - Une seule ligne
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total',
                    _statistiques!.totalColis.toString(),
                    Colors.blue,
                    Icons.inventory_2,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: _buildStatCard(
                    'Livrés',
                    _statistiques!.livres.toString(),
                    Colors.green,
                    Icons.check_circle,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: _buildStatCard(
                    'En retard',
                    _statistiques!.enRetard.toString(),
                    Colors.orange,
                    Icons.schedule,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: _buildStatCard(
                    'Annulés',
                    _statistiques!.annules.toString(),
                    Colors.red,
                    Icons.cancel,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: _buildStatCard(
                    'Retours',
                    _statistiques!.reportes.toString(),
                    Colors.purple,
                    Icons.event_busy,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),

            // Statistiques financières
            Column(
              children: [
                _buildFinancialRow(
                  'Total à encaisser',
                  _statistiques!.totalAEncaisser,
                  Colors.blue[600]!,
                ),
                const SizedBox(height: 8),
                _buildFinancialRow(
                  'Total frais livraison',
                  _statistiques!.totalFraisLivraison,
                  Colors.orange[600]!,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        _statistiques!.pointJournalier >= 0
                            ? Colors.green[50]
                            : Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          _statistiques!.pointJournalier >= 0
                              ? Colors.green[200]!
                              : Colors.red[200]!,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Point journalier',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color:
                              _statistiques!.pointJournalier >= 0
                                  ? Colors.green[800]
                                  : Colors.red[800],
                        ),
                      ),
                      Text(
                        '${_currencyFormat.format(_statistiques!.pointJournalier)} FCFA',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color:
                              _statistiques!.pointJournalier >= 0
                                  ? Colors.green[800]
                                  : Colors.red[800],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 10, color: color),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialRow(String label, double amount, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontWeight: FontWeight.w500, color: color),
        ),
        Text(
          '${_currencyFormat.format(amount)} FCFA',
          style: TextStyle(fontWeight: FontWeight.bold, color: color),
        ),
      ],
    );
  }

  Widget _buildColisCard(Colis colis) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      elevation: 2,
      child: InkWell(
        onTap: () => _naviguerVersModification(colis),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Première ligne : Photo + Infos principales + Statut
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Photo du colis (cliquable pour popup)
                  GestureDetector(
                    onTap: () => _afficherImagePopup(colis.photoPath),
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _buildImageWidget(
                          colis.photoPath,
                          fit: BoxFit.cover,
                          width: 60,
                          height: 60,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Informations principales
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          colis.libelle,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          colis.nomClient ?? 'Client non renseigné',
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          colis.numeroClient,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 8),

                  // Statut
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Color(
                        int.parse(colis.statut.couleur.replaceAll('#', '0xFF')),
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Color(
                          int.parse(
                            colis.statut.couleur.replaceAll('#', '0xFF'),
                          ),
                        ).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          colis.statut.emoji,
                          style: const TextStyle(fontSize: 14),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          colis.statut.libelle,
                          style: TextStyle(
                            color: Color(colis.statut.colorValue),
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Deuxième ligne : Informations détaillées + Heure
              Row(
                children: [
                  // Zone de livraison
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: Colors.blue[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          colis.zoneLivraison,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue[600],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Frais de livraison
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.local_shipping,
                          size: 16,
                          color: Colors.orange[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_currencyFormat.format(colis.fraisLivraison)} FCFA',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.orange[600],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Heure d'ajout
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        DateFormat('HH:mm').format(colis.dateAjout),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Troisième ligne : Montant + Boutons d'action
              Row(
                children: [
                  // Montant à encaisser
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.account_balance_wallet,
                          size: 16,
                          color: Colors.green[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_currencyFormat.format(colis.resteAPayer)} FCFA',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.green[600],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Boutons d'action
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue[200]!),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<StatutLivraison>(
                            value: colis.statut,
                            icon: Icon(
                              Icons.arrow_drop_down,
                              color: Colors.blue[600],
                              size: 18,
                            ),
                            onChanged: (StatutLivraison? nouveauStatut) {
                              if (nouveauStatut != null &&
                                  nouveauStatut != colis.statut) {
                                _changerStatutDirectement(colis, nouveauStatut);
                              }
                            },
                            items:
                                StatutLivraison.values.map((
                                  StatutLivraison statut,
                                ) {
                                  return DropdownMenuItem<StatutLivraison>(
                                    value: statut,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          statut.emoji,
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          statut.libelle,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Color(
                                              int.parse(
                                                statut.couleur.replaceAll(
                                                  '#',
                                                  '0xFF',
                                                ),
                                              ),
                                            ),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green[200]!),
                        ),
                        child: IconButton(
                          onPressed: () => _partagerColisPDF(colis),
                          icon: Icon(
                            Icons.picture_as_pdf,
                            size: 18,
                            color: Colors.green[600],
                          ),
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(
                            minWidth: 36,
                            minHeight: 36,
                          ),
                          tooltip: 'PDF',
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red[200]!),
                        ),
                        child: IconButton(
                          onPressed: () => _supprimerLivraison(colis),
                          icon: Icon(
                            Icons.delete,
                            size: 18,
                            color: Colors.red[600],
                          ),
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(
                            minWidth: 36,
                            minHeight: 36,
                          ),
                          tooltip: 'Supprimer',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Commandes du jour',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              _dateFormat.format(_dateSelectionnee),
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _ajouterLivraison,
            icon: const Icon(Icons.add),
            tooltip: 'Ajouter une livraison',
          ),
          IconButton(
            onPressed: _selectionnerDate,
            icon: const Icon(Icons.calendar_today),
            tooltip: 'Changer la date',
          ),
          if (_colis.isNotEmpty)
            IconButton(
              onPressed: _isExporting ? null : _exporterPDF,
              icon:
                  _isExporting
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.picture_as_pdf),
              tooltip: 'Exporter en PDF',
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Statistiques
                  _buildStatistiquesCard(),

                  // Liste des colis
                  Expanded(
                    child:
                        _colis.isEmpty
                            ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.inventory_2_outlined,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Aucune livraison pour cette date',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  TextButton.icon(
                                    onPressed: _selectionnerDate,
                                    icon: const Icon(Icons.calendar_today),
                                    label: const Text('Changer la date'),
                                  ),
                                ],
                              ),
                            )
                            : ListView.builder(
                              itemCount: _colis.length,
                              itemBuilder: (context, index) {
                                return _buildColisCard(_colis[index]);
                              },
                            ),
                  ),
                ],
              ),
    );
  }
}
