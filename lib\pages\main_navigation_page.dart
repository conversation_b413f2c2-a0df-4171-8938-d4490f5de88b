import 'package:flutter/material.dart';
import 'dashboard_page.dart';
import 'invoice_list_page.dart';
import 'product_list_page.dart';
import 'tasks_page.dart';
import 'whatsapp_page.dart';
import '../widgets/floating_navigation_bar.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _pages = [
    const DashboardPage(),
    const InvoiceListPage(),
    const ProductListPage(),
    const TasksPage(),
    const WhatsAppPage(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onNavigationTap(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }



  @override
  Widget build(BuildContext context) {
    // Constantes pour la barre de navigation
    const navHeight = 72.0;
    const navMargin = 12.0;

    return Scaffold(
      backgroundColor: Colors.transparent, // Suppression du fond blanc
      extendBody: true,
      // Utilisation d'un Stack pour overlay au lieu de bottomNavigationBar
      body: Stack(
        children: [
          // Contenu principal sans padding - chaque page gère son propre padding
          SafeArea(
            bottom: false, // SafeArea ne gère pas le bas car intégré dans la capsule
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              children: _pages,
            ),
          ),
          // Barre de navigation flottante réactivée
          // Barre de navigation flottante
          Positioned(
            left: navMargin,
            right: navMargin,
            bottom: navMargin,
            child: SafeArea(
              top: false,
              child: Container(
                height: navHeight,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF2196F3), // Bleu
                      Color(0xFF3F51B5), // Indigo
                      Color(0xFF9C27B0), // Violet
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(36),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: FloatingNavigationBar(
                  currentIndex: _currentIndex,
                  onTap: _onNavigationTap,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
