import 'package:flutter/material.dart';
import 'dashboard_page.dart';
import 'invoice_list_page.dart';
import 'product_list_page.dart';
import 'tasks_page.dart';
import 'whatsapp_page.dart';
import '../widgets/floating_navigation_bar.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _pages = [
    const DashboardPage(),
    const InvoiceListPage(),
    const ProductListPage(),
    const TasksPage(),
    const WhatsAppPage(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onNavigationTap(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 120),
            child: PageView(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              children: _pages,
            ),
          ),
          Positioned(
            bottom: 20, // Espace en bas pour l'effet flottant
            left: 0,
            right: 0,
            child: FloatingNavigationBar(
              currentIndex: _currentIndex,
              onTap: _onNavigationTap,
            ),
          ),
        ],
      ),
    );
  }
}
