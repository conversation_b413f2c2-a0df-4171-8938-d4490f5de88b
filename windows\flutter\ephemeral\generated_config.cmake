# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\G-n-ral-HCP-CRM" PROJECT_DIR)

set(FLUTTER_VERSION "2.0.1+3" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 2 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 1 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 3 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\G-n-ral-HCP-CRM"
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\G-n-ral-HCP-CRM\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\G-n-ral-HCP-CRM"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\G-n-ral-HCP-CRM\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\G-n-ral-HCP-CRM\\.dart_tool\\package_config.json"
)
