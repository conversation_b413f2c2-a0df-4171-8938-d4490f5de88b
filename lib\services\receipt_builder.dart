import 'package:flutter/services.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';

/// Configuration pour la génération de reçus
class ReceiptConfig {
  final String paperSize;
  final PrintEffect printEffect;
  final WatermarkSettings watermark;
  final bool smartCrop;
  final int copies;
  final int rotation; // 0, 90, 180, 270 degrés
  final bool mirror;

  ReceiptConfig({
    this.paperSize = PaperSizeConfig.mm58,
    this.printEffect = PrintEffect.original,
    this.watermark = const WatermarkSettings(),
    this.smartCrop = true,
    this.copies = 1,
    this.rotation = 0,
    this.mirror = false,
  });

  ReceiptConfig copyWith({
    String? paperSize,
    PrintEffect? printEffect,
    WatermarkSettings? watermark,
    bool? smartCrop,
    int? copies,
    int? rotation,
    bool? mirror,
  }) {
    return ReceiptConfig(
      paperSize: paperSize ?? this.paperSize,
      printEffect: printEffect ?? this.printEffect,
      watermark: watermark ?? this.watermark,
      smartCrop: smartCrop ?? this.smartCrop,
      copies: copies ?? this.copies,
      rotation: rotation ?? this.rotation,
      mirror: mirror ?? this.mirror,
    );
  }
}

/// Configuration des tailles de papier supportées
class PaperSizeConfig {
  static const mm58 = 'mm58';
  static const mm78x120 = 'mm78x120';
  static const mm80 = 'mm80';
  
  static const Map<String, String> displayNames = {
    mm58: 'Papier 58mm',
    mm78x120: 'Papier 78×120mm',
    mm80: 'Papier 80mm',
  };
  
  static const Map<String, int> widths = {
    mm58: 58,
    mm78x120: 78,
    mm80: 80,
  };
}

/// Effets d'impression
enum PrintEffect {
  original('Original'),
  blackWhite('Noir & Blanc'),
  dither('Tramage');

  const PrintEffect(this.displayName);
  final String displayName;
}

/// Configuration du filigrane
class WatermarkSettings {
  final WatermarkType type;
  final String? text;
  final String? imagePath;
  final Uint8List? imageData;
  final double opacity;

  const WatermarkSettings({
    this.type = WatermarkType.none,
    this.text,
    this.imagePath,
    this.imageData,
    this.opacity = 0.3,
  });

  WatermarkSettings copyWith({
    WatermarkType? type,
    String? text,
    String? imagePath,
    Uint8List? imageData,
    double? opacity,
  }) {
    return WatermarkSettings(
      type: type ?? this.type,
      text: text ?? this.text,
      imagePath: imagePath ?? this.imagePath,
      imageData: imageData ?? this.imageData,
      opacity: opacity ?? this.opacity,
    );
  }
}

/// Types de filigrane
enum WatermarkType {
  none('Aucun'),
  text('Texte'),
  image('Image');

  const WatermarkType(this.displayName);
  final String displayName;
}

/// Données pour la génération du reçu
class ReceiptData {
  final String? logoPath;
  final Uint8List? logoBytes;
  final String companyName;
  final String? companyAddress;
  final String? companyPhone;
  final String? companyEmail;
  final String? companyWebsite;
  
  final String title;
  final String? referenceNumber;
  final DateTime date;
  
  final String clientName;
  final String? clientNumber;
  final String? deliveryLocation;
  final String? deliveryDetails;
  
  final List<ReceiptItem> items;
  final double total;
  final String? notes;
  
  final List<QRCodeData> qrCodes;
  final String? footerText;

  ReceiptData({
    this.logoPath,
    this.logoBytes,
    required this.companyName,
    this.companyAddress,
    this.companyPhone,
    this.companyEmail,
    this.companyWebsite,
    required this.title,
    this.referenceNumber,
    required this.date,
    required this.clientName,
    this.clientNumber,
    this.deliveryLocation,
    this.deliveryDetails,
    this.items = const [],
    required this.total,
    this.notes,
    this.qrCodes = const [],
    this.footerText,
  });
}

/// Article du reçu
class ReceiptItem {
  final String name;
  final int quantity;
  final double unitPrice;
  final double total;

  ReceiptItem({
    required this.name,
    required this.quantity,
    required this.unitPrice,
    required this.total,
  });
}

/// Données QR Code
class QRCodeData {
  final String label;
  final String data;
  final QRCodeSize size;

  QRCodeData({
    required this.label,
    required this.data,
    this.size = QRCodeSize.medium,
  });
}

/// Tailles de QR Code
enum QRCodeSize {
  small(100),
  medium(150),
  large(200);

  const QRCodeSize(this.pixels);
  final int pixels;
}

/// Builder principal pour générer les reçus thermiques
class ReceiptBuilder {
  static final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy HH:mm', 'fr_FR');

  /// Génère un reçu thermique complet
  static Future<Uint8List> generateReceipt(
    ReceiptData data,
    ReceiptConfig config,
  ) async {
    try {
      debugPrint('🎫 Génération du reçu thermique...');
      
      // Charger le profil de capacité pour la taille de papier
      final profile = await CapabilityProfile.load();
      final paperSize = _getPaperSize(config.paperSize);
      final generator = Generator(paperSize, profile);
      
      List<int> bytes = [];
      
      // Initialisation
      bytes += generator.reset();
      
      // Logo et en-tête entreprise
      if (data.logoBytes != null || data.logoPath != null) {
        bytes += await _addLogo(generator, data);
      }
      
      bytes += _addCompanyHeader(generator, data);
      
      // Séparateur
      bytes += generator.hr();
      
      // Titre et référence
      bytes += _addTitleSection(generator, data);
      
      // Informations client
      bytes += _addClientInfo(generator, data);
      
      // Articles (si présents)
      if (data.items.isNotEmpty) {
        bytes += _addItemsSection(generator, data);
      }
      
      // Total
      bytes += _addTotalSection(generator, data);
      
      // Notes (si présentes)
      if (data.notes != null && data.notes!.isNotEmpty) {
        bytes += _addNotesSection(generator, data);
      }
      
      // QR Codes
      if (data.qrCodes.isNotEmpty) {
        bytes += await _addQRCodesSection(generator, data);
      }
      
      // Pied de page
      bytes += _addFooter(generator, data);
      
      // Appliquer les effets et transformations
      bytes = _applyEffects(bytes, config);
      
      // Couper le papier
      bytes += generator.cut();
      
      debugPrint('✅ Reçu généré: ${bytes.length} bytes');
      return Uint8List.fromList(bytes);
    } catch (e) {
      debugPrint('❌ Erreur génération reçu: $e');
      rethrow;
    }
  }

  /// Convertit la taille de papier personnalisée vers ESC/POS
  static PaperSize _getPaperSize(String size) {
    switch (size) {
      case PaperSizeConfig.mm58:
        return PaperSize.mm58;
      case PaperSizeConfig.mm78x120:
        return PaperSize.mm80; // Utiliser 80mm comme approximation
      case PaperSizeConfig.mm80:
        return PaperSize.mm80;
      default:
        return PaperSize.mm58;
    }
  }

  /// Ajoute le logo de l'entreprise
  static Future<List<int>> _addLogo(Generator generator, ReceiptData data) async {
    try {
      List<int> bytes = [];
      
      Uint8List? logoData = data.logoBytes;
      
      if (logoData == null && data.logoPath != null) {
        // Charger le logo depuis les assets
        final byteData = await rootBundle.load(data.logoPath!);
        logoData = byteData.buffer.asUint8List();
      }
      
      if (logoData != null) {
        // Convertir l'image pour l'impression thermique
        final image = await _processImageForThermal(logoData);
        if (image != null) {
          bytes += generator.imageRaster(image, align: PosAlign.center);
          bytes += generator.feed(1);
        }
      }
      
      return bytes;
    } catch (e) {
      debugPrint('⚠️ Erreur ajout logo: $e');
      return [];
    }
  }

  /// Ajoute l'en-tête de l'entreprise
  static List<int> _addCompanyHeader(Generator generator, ReceiptData data) {
    List<int> bytes = [];
    
    // Nom de l'entreprise
    bytes += generator.text(
      data.companyName,
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size2,
        width: PosTextSize.size2,
        bold: true,
      ),
    );
    
    // Adresse
    if (data.companyAddress != null) {
      bytes += generator.text(
        data.companyAddress!,
        styles: const PosStyles(align: PosAlign.center),
      );
    }
    
    // Téléphone
    if (data.companyPhone != null) {
      bytes += generator.text(
        'Tél: ${data.companyPhone!}',
        styles: const PosStyles(align: PosAlign.center),
      );
    }
    
    // Email
    if (data.companyEmail != null) {
      bytes += generator.text(
        data.companyEmail!,
        styles: const PosStyles(align: PosAlign.center),
      );
    }
    
    // Site web
    if (data.companyWebsite != null) {
      bytes += generator.text(
        data.companyWebsite!,
        styles: const PosStyles(align: PosAlign.center),
      );
    }
    
    bytes += generator.feed(1);
    return bytes;
  }

  /// Ajoute la section titre et référence
  static List<int> _addTitleSection(Generator generator, ReceiptData data) {
    List<int> bytes = [];
    
    // Titre
    bytes += generator.text(
      data.title,
      styles: const PosStyles(
        align: PosAlign.center,
        bold: true,
        height: PosTextSize.size2,
      ),
    );
    
    // Référence
    if (data.referenceNumber != null) {
      bytes += generator.text(
        'Réf: ${data.referenceNumber!}',
        styles: const PosStyles(align: PosAlign.center),
      );
    }
    
    // Date
    bytes += generator.text(
      _dateFormat.format(data.date),
      styles: const PosStyles(align: PosAlign.center),
    );
    
    bytes += generator.feed(1);
    return bytes;
  }

  /// Ajoute les informations client
  static List<int> _addClientInfo(Generator generator, ReceiptData data) {
    List<int> bytes = [];
    
    bytes += generator.text(
      'CLIENT:',
      styles: const PosStyles(bold: true),
    );
    
    bytes += generator.text(data.clientName);
    
    if (data.clientNumber != null) {
      bytes += generator.text('N°: ${data.clientNumber!}');
    }
    
    if (data.deliveryLocation != null) {
      bytes += generator.text('Lieu: ${data.deliveryLocation!}');
    }
    
    if (data.deliveryDetails != null) {
      bytes += generator.text('Détails: ${data.deliveryDetails!}');
    }
    
    bytes += generator.feed(1);
    return bytes;
  }

  /// Ajoute la section des articles
  static List<int> _addItemsSection(Generator generator, ReceiptData data) {
    List<int> bytes = [];
    
    bytes += generator.hr();
    bytes += generator.text(
      'ARTICLES:',
      styles: const PosStyles(bold: true),
    );
    
    for (var item in data.items) {
      // Nom de l'article
      bytes += generator.text(item.name);
      
      // Quantité x Prix unitaire = Total
      final line = '${item.quantity} x ${_formatCurrency(item.unitPrice)} = ${_formatCurrency(item.total)}';
      bytes += generator.text(
        line,
        styles: const PosStyles(align: PosAlign.right),
      );
    }
    
    bytes += generator.feed(1);
    return bytes;
  }

  /// Ajoute la section total
  static List<int> _addTotalSection(Generator generator, ReceiptData data) {
    List<int> bytes = [];
    
    bytes += generator.hr();
    bytes += generator.text(
      'TOTAL: ${_formatCurrency(data.total)} FCFA',
      styles: const PosStyles(
        align: PosAlign.center,
        bold: true,
        height: PosTextSize.size2,
        width: PosTextSize.size2,
      ),
    );
    bytes += generator.hr();
    
    return bytes;
  }

  /// Ajoute la section notes
  static List<int> _addNotesSection(Generator generator, ReceiptData data) {
    List<int> bytes = [];
    
    bytes += generator.text(
      'NOTES:',
      styles: const PosStyles(bold: true),
    );
    bytes += generator.text(data.notes!);
    bytes += generator.feed(1);
    
    return bytes;
  }

  /// Ajoute la section QR codes
  static Future<List<int>> _addQRCodesSection(Generator generator, ReceiptData data) async {
    List<int> bytes = [];
    
    bytes += generator.text(
      'CODES QR:',
      styles: const PosStyles(
        align: PosAlign.center,
        bold: true,
      ),
    );
    
    for (var qrData in data.qrCodes) {
      try {
        // Générer le QR code
        bytes += generator.qrcode(
          qrData.data,
          size: QRSize.size6,
          cor: QRCorrection.M,
          align: PosAlign.center,
        );
        
        // Label du QR code
        bytes += generator.text(
          qrData.label,
          styles: const PosStyles(
            align: PosAlign.center,
            bold: true,
          ),
        );
        
        bytes += generator.feed(1);
      } catch (e) {
        debugPrint('⚠️ Erreur génération QR code ${qrData.label}: $e');
      }
    }
    
    return bytes;
  }

  /// Ajoute le pied de page
  static List<int> _addFooter(Generator generator, ReceiptData data) {
    List<int> bytes = [];
    
    if (data.footerText != null) {
      bytes += generator.text(
        data.footerText!,
        styles: const PosStyles(align: PosAlign.center),
      );
    }
    
    bytes += generator.text(
      'Merci de votre confiance!',
      styles: const PosStyles(
        align: PosAlign.center,
        bold: true,
      ),
    );
    
    bytes += generator.feed(3);
    return bytes;
  }

  /// Applique les effets d'impression
  static List<int> _applyEffects(List<int> bytes, ReceiptConfig config) {
    // Pour l'instant, retourner les bytes tels quels
    // Les effets comme rotation, miroir, etc. peuvent être implémentés ici
    return bytes;
  }

  /// Traite une image pour l'impression thermique
  static Future<img.Image?> _processImageForThermal(Uint8List imageData) async {
    try {
      // Décoder l'image avec le package image
      final img.Image? decodedImage = img.decodeImage(imageData);
      if (decodedImage == null) {
        throw Exception('Impossible de décoder l\'image');
      }

      // Redimensionner si nécessaire (largeur max 384px pour 58mm)
      img.Image resizedImage = decodedImage;
      if (decodedImage.width > 384) {
        resizedImage = img.copyResize(decodedImage, width: 384);
      }

      // Convertir en noir et blanc
      final img.Image bwImage = img.grayscale(resizedImage);
      
      return bwImage;
    } catch (e) {
      debugPrint('❌ Erreur traitement image: $e');
      return null;
    }
  }

  /// Formate un montant en devise
  static String _formatCurrency(double amount) {
    return _currencyFormat.format(amount).replaceAll('\u00A0', ' ');
  }

  /// Génère un reçu de test
  static Future<Uint8List> generateTestReceipt() async {
    final testData = ReceiptData(
      companyName: 'HCP DESIGN',
      companyAddress: 'Abidjan, Côte d\'Ivoire',
      companyPhone: '+225 07 09 49 58 48',
      companyEmail: '<EMAIL>',
      companyWebsite: 'www.hcp-designci.com',
      title: 'MINI FACTURE',
      referenceNumber: 'TEST001',
      date: DateTime.now(),
      clientName: 'Client Test',
      clientNumber: '12345',
      deliveryLocation: 'Abidjan',
      items: [
        ReceiptItem(
          name: 'Coque personnalisée',
          quantity: 1,
          unitPrice: 15000,
          total: 15000,
        ),
      ],
      total: 15000,
      qrCodes: [
        QRCodeData(
          label: 'Site Web',
          data: 'https://www.hcp-designci.com',
        ),
        QRCodeData(
          label: 'WhatsApp',
          data: 'https://wa.me/2250709495848',
        ),
      ],
      footerText: 'Impression test',
    );
    
    final config = ReceiptConfig();
    return generateReceipt(testData, config);
  }
}