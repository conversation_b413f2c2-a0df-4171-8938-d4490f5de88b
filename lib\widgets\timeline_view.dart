import 'package:flutter/material.dart';
import '../models/advanced_task.dart';

class TimelineView extends StatefulWidget {
  final List<AdvancedTask> tasks;
  final Function(AdvancedTask) onTaskTap;

  const TimelineView({
    super.key,
    required this.tasks,
    required this.onTaskTap,
  });

  @override
  State<TimelineView> createState() => _TimelineViewState();
}

class _TimelineViewState extends State<TimelineView> {
  final ScrollController _scrollController = ScrollController();
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now().add(const Duration(days: 90));
  double _dayWidth = 40.0;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tasksWithDates = widget.tasks
        .where((task) => task.startDate != null || task.dueDate != null)
        .toList();

    return Column(
      children: [
        _buildTimelineHeader(),
        Expanded(
          child: Row(
            children: [
              _buildTaskLabels(tasksWithDates),
              Expanded(
                child: _buildTimelineChart(tasksWithDates),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.timeline,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Text(
            'Timeline des tâches',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const Spacer(),
          _buildZoomControls(),
        ],
      ),
    );
  }

  Widget _buildZoomControls() {
    return Row(
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _dayWidth = (_dayWidth * 1.2).clamp(20.0, 100.0);
            });
          },
          icon: const Icon(Icons.zoom_in),
          tooltip: 'Zoom avant',
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _dayWidth = (_dayWidth / 1.2).clamp(20.0, 100.0);
            });
          },
          icon: const Icon(Icons.zoom_out),
          tooltip: 'Zoom arrière',
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: _scrollToToday,
          icon: const Icon(Icons.today),
          label: const Text("Aujourd'hui"),
        ),
      ],
    );
  }

  Widget _buildTaskLabels(List<AdvancedTask> tasks) {
    return Container(
      width: 250,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          _buildDateHeader(),
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                final task = tasks[index];
                return _buildTaskLabel(task);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateHeader() {
    return Container(
      height: 60,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: const Align(
        alignment: Alignment.centerLeft,
        child: Text(
          'Tâches',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildTaskLabel(AdvancedTask task) {
    return Container(
      height: 60,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.1)),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: double.infinity,
            decoration: BoxDecoration(
              color: task.priority.color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  task.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    _buildStatusChip(task.status),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.flag,
                      color: task.priority.color,
                      size: 12,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineChart(List<AdvancedTask> tasks) {
    return Column(
      children: [
        _buildTimelineHeader2(),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: _calculateTimelineWidth(),
              child: Column(
                children: [
                  _buildDateScale(),
                  Expanded(
                    child: ListView.builder(
                      itemCount: tasks.length,
                      itemBuilder: (context, index) {
                        final task = tasks[index];
                        return _buildTimelineBar(task);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineHeader2() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: const Center(
        child: Text(
          'Planning',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildDateScale() {
    final totalDays = _endDate.difference(_startDate).inDays;
    
    return Container(
      height: 40,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Row(
        children: List.generate(totalDays, (index) {
          final date = _startDate.add(Duration(days: index));
          final isToday = _isSameDay(date, DateTime.now());
          
          return Container(
            width: _dayWidth,
            decoration: BoxDecoration(
              color: isToday ? Theme.of(context).primaryColor.withValues(alpha: 0.1) : null,
              border: Border(
                right: BorderSide(color: Colors.grey.withValues(alpha: 0.1)),
              ),
            ),
            child: Center(
              child: Text(
                '${date.day}',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                  color: isToday ? Theme.of(context).primaryColor : null,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildTimelineBar(AdvancedTask task) {
    final startDate = task.startDate ?? task.dueDate!;
    final endDate = task.dueDate ?? task.startDate!;
    
    final startOffset = startDate.difference(_startDate).inDays * _dayWidth;
    final duration = endDate.difference(startDate).inDays.abs();
    final barWidth = duration == 0 ? _dayWidth : duration * _dayWidth;

    return Container(
      height: 60,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.1)),
        ),
      ),
      child: Stack(
        children: [
          if (startOffset >= 0)
            Positioned(
              left: startOffset,
              top: 15,
              child: GestureDetector(
                onTap: () => widget.onTaskTap(task),
                child: Container(
                  width: barWidth.clamp(_dayWidth / 2, double.infinity),
                  height: 30,
                  decoration: BoxDecoration(
                    color: task.priority.color.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: task.priority.color,
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      task.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(TaskStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getStatusColor(status).withValues(alpha: 0.3)),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          fontSize: 9,
          color: _getStatusColor(status),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.inReview:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.blocked:
        return Colors.red;
    }
  }

  double _calculateTimelineWidth() {
    final totalDays = _endDate.difference(_startDate).inDays;
    return totalDays * _dayWidth;
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  void _scrollToToday() {
    final todayOffset = DateTime.now().difference(_startDate).inDays * _dayWidth;
    // TODO: Implémenter le scroll vers aujourd'hui
  }
}
