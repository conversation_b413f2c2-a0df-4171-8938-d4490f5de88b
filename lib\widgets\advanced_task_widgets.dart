import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/advanced_task.dart';

/// Widget pour afficher une tâche dans une liste
class TaskListTile extends StatelessWidget {
  final AdvancedTask task;
  final VoidCallback? onTap;
  final Function(TaskStatus)? onStatusChanged;

  const TaskListTile({
    super.key,
    required this.task,
    this.onTap,
    this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: _buildStatusIndicator(context),
        title: Text(
          task.title,
          style: TextStyle(
            decoration: task.isCompleted ? TextDecoration.lineThrough : null,
            color: task.isCompleted ? Colors.grey : null,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (task.description.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                task.description,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: task.isCompleted ? Colors.grey : null,
                ),
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                _buildPriorityChip(),
                const SizedBox(width: 8),
                if (task.dueDate != null) _buildDueDateChip(context),
                const Spacer(),
                if (task.assigneeId != null) _buildAssigneeAvatar(),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<TaskStatus>(
          icon: const Icon(Icons.more_vert),
          onSelected: onStatusChanged,
          itemBuilder: (context) => TaskStatus.values.map((status) =>
            PopupMenuItem(
              value: status,
              child: Row(
                children: [
                  Icon(_getStatusIcon(status), color: _getStatusColor(status)),
                  const SizedBox(width: 8),
                  Text(status.displayName),
                ],
              ),
            ),
          ).toList(),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildStatusIndicator(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getStatusColor(task.status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStatusColor(task.status),
          width: 2,
        ),
      ),
      child: Icon(
        _getStatusIcon(task.status),
        color: _getStatusColor(task.status),
        size: 20,
      ),
    );
  }

  Widget _buildPriorityChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: task.priority.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: task.priority.color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.flag,
            size: 12,
            color: task.priority.color,
          ),
          const SizedBox(width: 4),
          Text(
            task.priority.displayName,
            style: TextStyle(
              fontSize: 12,
              color: task.priority.color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDueDateChip(BuildContext context) {
    final now = DateTime.now();
    final isOverdue = task.dueDate!.isBefore(now) && !task.isCompleted;
    final isToday = DateUtils.isSameDay(task.dueDate!, now);
    final isTomorrow = DateUtils.isSameDay(
      task.dueDate!,
      now.add(const Duration(days: 1)),
    );

    Color chipColor;
    String label;

    if (isOverdue) {
      chipColor = Colors.red;
      label = 'En retard';
    } else if (isToday) {
      chipColor = Colors.orange;
      label = 'Aujourd\'hui';
    } else if (isTomorrow) {
      chipColor = Colors.blue;
      label = 'Demain';
    } else {
      chipColor = Colors.grey;
      label = DateFormat('dd/MM').format(task.dueDate!);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.schedule,
            size: 12,
            color: chipColor,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: chipColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssigneeAvatar() {
    return CircleAvatar(
      radius: 12,
      backgroundColor: Colors.blue.withOpacity(0.1),
      child: const Icon(
        Icons.person,
        size: 16,
        color: Colors.blue,
      ),
    );
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.inReview:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.blocked:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Icons.radio_button_unchecked;
      case TaskStatus.inProgress:
        return Icons.play_circle;
      case TaskStatus.inReview:
        return Icons.rate_review;
      case TaskStatus.completed:
        return Icons.check_circle;
      case TaskStatus.blocked:
        return Icons.block;
    }
  }
}

/// Widget pour afficher une carte de projet
class ProjectCard extends StatelessWidget {
  final Project project;
  final VoidCallback? onTap;
  final int taskCount;
  final int completedCount;

  const ProjectCard({
    super.key,
    required this.project,
    this.onTap,
    required this.taskCount,
    required this.completedCount,
  });

  @override
  Widget build(BuildContext context) {
    final progress = taskCount > 0 ? completedCount / taskCount : 0.0;

    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: project.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      project.iconData != null 
                          ? IconData(int.parse(project.iconData!), fontFamily: 'MaterialIcons')
                          : Icons.folder,
                      color: project.color,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          project.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          project.status.displayName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: _getStatusColor(project.status),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              if (project.description.isNotEmpty) ...[
                Text(
                  project.description,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 12),
              ],
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Progression',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.grey.withOpacity(0.2),
                          valueColor: AlwaysStoppedAnimation<Color>(project.color),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    '$completedCount/$taskCount',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(ProjectStatus status) {
    switch (status) {
      case ProjectStatus.active:
        return Colors.green;
      case ProjectStatus.onHold:
        return Colors.orange;
      case ProjectStatus.completed:
        return Colors.blue;
      case ProjectStatus.archived:
        return Colors.grey;
    }
  }
}

/// Widget pour afficher les tags d'une tâche
class TaskTagsWidget extends StatelessWidget {
  final List<String> tags;
  final int maxVisible;

  const TaskTagsWidget({
    super.key,
    required this.tags,
    this.maxVisible = 3,
  });

  @override
  Widget build(BuildContext context) {
    if (tags.isEmpty) return const SizedBox.shrink();

    final visibleTags = tags.take(maxVisible).toList();
    final remainingCount = tags.length - maxVisible;

    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: [
        ...visibleTags.map((tag) => _buildTag(context, tag)),
        if (remainingCount > 0)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '+$remainingCount',
              style: const TextStyle(
                fontSize: 10,
                color: Colors.grey,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTag(BuildContext context, String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.3),
        ),
      ),
      child: Text(
        tag,
        style: TextStyle(
          fontSize: 10,
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
