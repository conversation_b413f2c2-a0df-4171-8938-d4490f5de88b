import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/invoice_service.dart';
import '../models/invoice.dart';

class MonthlyRevenuePage extends StatefulWidget {
  const MonthlyRevenuePage({super.key});

  @override
  State<MonthlyRevenuePage> createState() => _MonthlyRevenuePageState();
}

class _MonthlyRevenuePageState extends State<MonthlyRevenuePage> {
  Map<String, double> _monthlyRevenues = {};
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  @override
  void initState() {
    super.initState();
    _loadMonthlyRevenues();
  }

  Future<void> _loadMonthlyRevenues() async {
    setState(() {
      _monthlyRevenues = {};
    });

    try {
      final invoices = await InvoiceService.loadInvoices();
      final Map<String, double> revenues = {};

      // Grouper les factures par mois
      for (final invoice in invoices) {
        if (invoice.status == InvoiceStatus.payee) {
          final monthKey = DateFormat('yyyy-MM').format(invoice.createdAt);
          revenues[monthKey] =
              (revenues[monthKey] ?? 0) +
              InvoiceService().calculateSubtotal(invoice.items);
        }
      }

      if (mounted) {
        setState(() {
          _monthlyRevenues = revenues;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HCP-DESIGN - CA Mensuels'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF2196F3), // Bleu
                Color(0xFF3F51B5), // Indigo
                Color(0xFF9C27B0), // Violet
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMonthlyRevenues,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: _monthlyRevenues.isEmpty ? _buildEmptyState() : _buildRevenueList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.trending_up, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucun chiffre d\'affaires',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Les revenus apparaîtront ici une fois les factures payées',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueList() {
    // Trier les mois par ordre décroissant (plus récent en premier)
    final sortedEntries =
        _monthlyRevenues.entries.toList()
          ..sort((a, b) => b.key.compareTo(a.key));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedEntries.length,
      itemBuilder: (context, index) {
        final entry = sortedEntries[index];
        final monthKey = entry.key;
        final revenue = entry.value;

        // Formater le mois
        final date = DateTime.parse('$monthKey-01');
        final monthName = DateFormat('MMMM yyyy', 'fr_FR').format(date);

        return _buildRevenueCard(monthName, revenue, index == 0);
      },
    );
  }

  Widget _buildRevenueCard(
    String monthName,
    double revenue,
    bool isCurrentMonth,
  ) {
    return Card(
      elevation: isCurrentMonth ? 8 : 4,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient:
              isCurrentMonth
                  ? LinearGradient(
                    colors: [Colors.blue[600]!, Colors.blue[400]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: isCurrentMonth ? null : Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      isCurrentMonth
                          ? Colors.white.withValues(alpha: 0.2)
                          : Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  isCurrentMonth ? Icons.star : Icons.calendar_month,
                  color: isCurrentMonth ? Colors.white : Colors.blue[600],
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      monthName.toUpperCase(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isCurrentMonth ? Colors.white : Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_currencyFormat.format(revenue)} FCFA',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: isCurrentMonth ? Colors.white : Colors.blue[600],
                      ),
                    ),
                    if (isCurrentMonth) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Mois en cours',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (isCurrentMonth)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'ACTUEL',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
