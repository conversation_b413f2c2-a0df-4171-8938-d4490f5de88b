import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/printer_service.dart';
import '../../services/receipt_builder.dart';
import '../../services/pdf_service.dart';
import '../../models/invoice.dart';
import '../pdf_preview_page.dart';

class PrintPreviewPage extends StatefulWidget {
  final ReceiptConfig config;
  final Invoice? invoice;
  final String? pdfPath;

  const PrintPreviewPage.fromInvoice({
    super.key,
    required this.invoice,
    required this.config,
  }) : pdfPath = null;

  const PrintPreviewPage.fromPdf({
    super.key,
    required this.pdfPath,
    required this.config,
  }) : invoice = null;

  @override
  State<PrintPreviewPage> createState() => _PrintPreviewPageState();
}

class _PrintPreviewPageState extends State<PrintPreviewPage> {
  late PrinterService _printerService;
  late ReceiptConfig _config;
  String? _previewTicket;
  bool _isPrinting = false;
  String? _errorMessage;
  bool _isConnected = false;

  @override
  void initState() {
    super.initState();
    _printerService = context.read<PrinterService>();
    _config = widget.config;
    _checkConnectionStatus();
    _generateTicketPreview();
  }

  Future<void> _checkConnectionStatus() async {
    try {
      final isConnected =
          _printerService.connectionStatus == PrinterConnectionStatus.connected;
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Erreur de connexion: $e';
        });
      }
    }
  }

  Future<void> _generateTicketPreview() async {
    try {
      // Utiliser directement la vraie mini facture PDF au lieu du format texte
      if (widget.invoice != null) {
        final invoice = widget.invoice!;
        final resteAPayer = invoice.total;

        // Générer la vraie mini facture PDF (comme dans l'aperçu n°2)
        final pdfDocument = await PDFService.generateMiniInvoice(
          nomClient: invoice.clientName,
          numeroClient: invoice.clientNumber,
          lieuLivraison: invoice.deliveryLocation,
          resteAPayer: resteAPayer,
          referenceFacture: 'F${invoice.id.toString().padLeft(6, '0')}',
          produitArticle:
              invoice.items.isNotEmpty ? invoice.items.first.name : null,
          dateLivraison: DateTime.now(),
          detailsLivraison: invoice.deliveryDetails,
        );

        if (mounted) {
          // Naviguer vers la vraie prévisualisation PDF (aperçu n°2)
          final fileName =
              'mini_facture_${invoice.clientNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf';

          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder:
                  (context) => PDFPreviewPage(
                    pdfDocument: pdfDocument,
                    title: 'Aperçu Mini Facture',
                    fileName: fileName,
                  ),
            ),
          );
        }
      } else {
        // Fallback pour les PDFs sans facture
        final ticketData = await _generateTicketDataFromPDF();
        final preview = _generatePreviewTextFromData(ticketData);

        if (mounted) {
          setState(() {
            _previewTicket = preview;
            _errorMessage = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Erreur de génération: $e';
        });
      }
    }
  }

  // Génère les données de mini-facture en utilisant le même service que l'écran factures
  Future<ReceiptData> _generateTicketDataFromPDF() async {
    if (widget.invoice != null) {
      // Utiliser le même service PDFService que l'écran factures
      final invoice = widget.invoice!;
      final resteAPayer = invoice.total;

      // Générer la mini facture PDF avec le même format que l'aperçu n°2
      await PDFService.generateMiniInvoice(
        nomClient: invoice.clientName,
        numeroClient: invoice.clientNumber,
        lieuLivraison: invoice.deliveryLocation,
        resteAPayer: resteAPayer,
        referenceFacture: 'F${invoice.id.toString().padLeft(6, '0')}',
        produitArticle:
            invoice.items.isNotEmpty ? invoice.items.first.name : null,
        dateLivraison: DateTime.now(),
        detailsLivraison: invoice.deliveryDetails,
      );

      // Convertir les données PDF en format thermique
      return ReceiptData(
        companyName: 'GENERAL HCP CRM',
        companyAddress:
            'Système de gestion intégré\nTél: +225 01 02 03 04 05\nEmail: <EMAIL>',
        title: 'MINI FACTURE',
        referenceNumber: 'F${invoice.id.toString().padLeft(6, '0')}',
        date: DateTime.now(),
        clientName: invoice.clientName,
        clientNumber: invoice.clientNumber,
        deliveryLocation: invoice.deliveryLocation,
        items: [], // Pas d'articles détaillés dans la mini facture
        total: resteAPayer,
        notes: 'Reste à payer',
        qrCodes: [
          QRCodeData(
            label: 'QR de paiement Wave',
            data: 'WAVE-PAYMENT-${invoice.id}',
          ),
          QRCodeData(
            label: 'QR de paiement Orange',
            data: 'ORANGE-PAYMENT-${invoice.id}',
          ),
        ],
        footerText: 'Mini Facture - GENERAL HCP CRM',
      );
    } else {
      // Génération des données de mini-facture depuis un PDF
      return ReceiptData(
        companyName: 'GENERAL HCP CRM',
        companyAddress:
            'Système de gestion intégré\nTél: +225 01 02 03 04 05\nEmail: <EMAIL>',
        title: 'MINI FACTURE PDF',
        referenceNumber: '#PDF-${DateTime.now().millisecondsSinceEpoch}',
        date: DateTime.now(),
        clientName: 'Document PDF',
        items: [],
        total: 0.0,
        notes: 'Document converti en mini facture',
        qrCodes: [
          QRCodeData(
            label: 'QR Code Mini Facture',
            data: 'PDF-FACTURE-${DateTime.now().millisecondsSinceEpoch}',
          ),
        ],
        footerText: 'Mini Facture Thermique - GENERAL HCP CRM',
      );
    }
  }

  String _generatePreviewTextFromData(ReceiptData data) {
    final invoice = widget.invoice;
    if (invoice != null) {
      return '''
╔══════════════════════════════════════╗
║            MINI FACTURE              ║
╠══════════════════════════════════════╣
║                                      ║
║  GENERAL HCP CRM                     ║
║  Système de gestion intégré          ║
║  Tél: +225 01 02 03 04 05            ║
║  Email: <EMAIL>        ║
║                                      ║
╠══════════════════════════════════════╣
║                                      ║
║  FACTURE N°: ${data.referenceNumber}              ║
║                                      ║
║  Date: ${data.date.toString().substring(0, 16)}        ║
║  Client: ${invoice.clientName}                     ║
║  Numéro du client                    ║
║  ${invoice.clientNumber}                           ║
║  Lieu de livraison                   ║
║  ${invoice.deliveryLocation}                       ║
║                                      ║
║  Reste à payer                       ║
║  ${data.total.toStringAsFixed(0)} FCFA                        ║
║                                      ║
║  QR de paiement                      ║
║  Wave            Orange              ║
║                                      ║
╚══════════════════════════════════════╝''';
    } else {
      return '''
╔══════════════════════════════════════╗
║            MINI FACTURE              ║
╠══════════════════════════════════════╣
║                                      ║
║  GENERAL HCP CRM                     ║
║  Système de gestion intégré          ║
║  Tél: +225 01 02 03 04 05            ║
║  Email: <EMAIL>        ║
║                                      ║
╠══════════════════════════════════════╣
║                                      ║
║  Document PDF converti               ║
║                                      ║
╚══════════════════════════════════════╝''';
    }
  }

  Future<void> _printTicket() async {
    if (!_isConnected) {
      _showErrorDialog('Imprimante non connectée');
      return;
    }

    setState(() {
      _isPrinting = true;
      _errorMessage = null;
    });

    try {
      final data = await _generateTicketDataFromPDF();
      final bytes = await ReceiptBuilder.generateReceipt(data, _config);

      await _printerService.printBytes(bytes);

      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Erreur d\'impression: $e';
        });
        _showErrorDialog(_errorMessage!);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPrinting = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Succès'),
            content: const Text('Mini facture imprimée avec succès !'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop(); // Retour à l'écran précédent
                },
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Erreur'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Aperçu Mini Facture'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _generateTicketPreview,
            icon: const Icon(Icons.refresh),
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: Column(
        children: [
          // Status bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            color: _isConnected ? Colors.green[100] : Colors.red[100],
            child: Row(
              children: [
                Icon(
                  _isConnected ? Icons.check_circle : Icons.error,
                  color: _isConnected ? Colors.green[700] : Colors.red[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _isConnected
                      ? 'Imprimante connectée'
                      : 'Imprimante déconnectée',
                  style: TextStyle(
                    color: _isConnected ? Colors.green[700] : Colors.red[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Preview section
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[600],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Aperçu Mini Facture',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Text(
                          _previewTicket ?? 'Génération en cours...',
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                            height: 1.2,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Error message
          if (_errorMessage != null)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[300]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.error, color: Colors.red[700], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(color: Colors.red[700]),
                    ),
                  ),
                ],
              ),
            ),

          // Control buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Print button
                Expanded(
                  flex: 2,
                  child: ElevatedButton.icon(
                    onPressed:
                        _isPrinting || !_isConnected ? null : _printTicket,
                    icon:
                        _isPrinting
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : const Icon(Icons.print),
                    label: Text(_isPrinting ? 'Impression...' : 'Imprimer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Copies control
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Copies:',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed:
                            _config.copies > 1
                                ? () {
                                  setState(() {
                                    _config = _config.copyWith(
                                      copies: _config.copies - 1,
                                    );
                                  });
                                }
                                : null,
                        icon: const Icon(Icons.remove),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          foregroundColor: Colors.black87,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '${_config.copies}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed:
                            _config.copies < 10
                                ? () {
                                  setState(() {
                                    _config = _config.copyWith(
                                      copies: _config.copies + 1,
                                    );
                                  });
                                }
                                : null,
                        icon: const Icon(Icons.add),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          foregroundColor: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
