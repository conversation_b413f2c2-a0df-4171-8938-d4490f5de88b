import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:provider/provider.dart';
import '../../services/printer_service.dart';
import '../../services/receipt_builder.dart';
import '../../models/invoice.dart';

class PrintPreviewPage extends StatefulWidget {
  final ReceiptConfig config;
  final Invoice? invoice;
  final String? pdfPath;

  const PrintPreviewPage.fromInvoice({
    super.key,
    required this.invoice,
    required this.config,
  }) : pdfPath = null;

  const PrintPreviewPage.fromPdf({
    super.key,
    required this.pdfPath,
    required this.config,
  }) : invoice = null;

  @override
  State<PrintPreviewPage> createState() => _PrintPreviewPageState();
}

class _PrintPreviewPageState extends State<PrintPreviewPage> {
  late PrinterService _printerService;
  late ReceiptConfig _config;
  bool _isPrinting = false;
  String? _errorMessage;
  bool _isConnected = false;
  int _printCopies = 1;
  int _selectedRotation = 0; // 0°, 90°, 180°, 270°
  int _selectedMirror = 0; // 0: normal, 1: horizontal, 2: vertical

  @override
  void initState() {
    super.initState();
    _printerService = context.read<PrinterService>();
    _config = widget.config;
    _checkConnectionStatus();
    _listenToConnectionStatus();
    _updateConfigFromUI();
  }

  void _updateConfigFromUI() {
    _config = _config.copyWith(
      copies: _printCopies,
      rotation: _selectedRotation,
      mirror: _selectedMirror > 0,
    );
    debugPrint(
      '🔧 Config UI mise à jour: copies=$_printCopies, rotation=$_selectedRotation, mirror=${_selectedMirror > 0}',
    );
  }

  void _listenToConnectionStatus() {
    // Écouter les changements de statut de connexion
    _printerService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _isConnected = status == PrinterConnectionStatus.connected;
        });
        debugPrint('🔄 Statut connexion mis à jour: $status');
      }
    });
  }

  Future<void> _checkConnectionStatus() async {
    try {
      final isConnected =
          _printerService.connectionStatus == PrinterConnectionStatus.connected;
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
        debugPrint(
          '🔍 Vérification connexion: ${isConnected ? "Connectée" : "Déconnectée"}',
        );
      }
    } catch (e) {
      debugPrint('❌ Erreur vérification connexion: $e');
      if (mounted) {
        setState(() {
          _isConnected = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Printing preview'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [IconButton(icon: const Icon(Icons.print), onPressed: () {})],
      ),
      body: Column(
        children: [
          // Preview de la mini facture
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Aperçu de la mini facture
                  _buildMiniInvoicePreview(),

                  const SizedBox(height: 32),

                  // Device status
                  _buildDeviceStatusSection(),

                  const SizedBox(height: 24),

                  // Print direction
                  _buildPrintDirectionSection(),

                  const SizedBox(height: 24),

                  // Mirror
                  _buildMirrorSection(),

                  const SizedBox(height: 24),

                  // Number of print copies
                  _buildPrintCopiesSection(),
                ],
              ),
            ),
          ),

          // Print button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: _isPrinting ? null : _printTicket,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child:
                  _isPrinting
                      ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Impression en cours...',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      )
                      : const Text(
                        'Print',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiniInvoicePreview() {
    return Container(
      width: double.infinity,
      height: 400,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // Header avec dimensions
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '78*120mm',
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          // Contenu de la mini facture
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child:
                  widget.pdfPath != null
                      ? _buildPdfPreview()
                      : _buildInvoiceContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceContent() {
    final invoice = widget.invoice;
    return Column(
      children: [
        // Référence
        Text(
          'Ref: ${invoice != null ? 'F${invoice.id.toString().padLeft(6, '0')}' : 'EXEMPLE-001'}',
          style: const TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        // Logo et QR codes
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
              ),
              child: const Icon(Icons.qr_code, size: 30),
            ),
            const Text(
              'HCP\nDESIGN',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
              ),
              child: const Icon(Icons.qr_code, size: 30),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Informations client
        Column(
          children: [
            const Text(
              'Nom du client',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              invoice?.clientName ?? 'Miracle',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            const Text(
              'Numéro du client',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              invoice?.clientNumber ?? '+225 0777547651',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            const Text(
              'Lieu de livraison',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              invoice?.deliveryLocation ?? 'Cocody',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            if (invoice?.deliveryDetails != null) ...[
              const Text(
                'Détails livraison',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
              Text(
                invoice!.deliveryDetails!,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
            ],

            const Text(
              'Date',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text('21/08/2025', style: const TextStyle(fontSize: 14)),
            const SizedBox(height: 8),

            const Text(
              'Reste à payer',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
            Text(
              '${invoice?.total.toStringAsFixed(0) ?? '2 500'} FCFA',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),

        const Spacer(),

        // QR de paiement
        const Text(
          'QR de paiement',
          style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Column(
              children: [
                const Text('Wave', style: TextStyle(fontSize: 10)),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black),
                  ),
                  child: const Icon(Icons.qr_code, size: 20),
                ),
              ],
            ),
            Column(
              children: [
                const Text('Orange', style: TextStyle(fontSize: 10)),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black),
                  ),
                  child: const Icon(Icons.qr_code, size: 20),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPdfPreview() {
    if (widget.pdfPath == null || !File(widget.pdfPath!).existsSync()) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red),
            SizedBox(height: 8),
            Text('PDF non trouvé', style: TextStyle(color: Colors.red)),
          ],
        ),
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: PDFView(
        filePath: widget.pdfPath!,
        enableSwipe: false,
        swipeHorizontal: false,
        autoSpacing: false,
        pageFling: false,
        pageSnap: false,
        fitPolicy: FitPolicy.BOTH,
        onError: (error) {
          debugPrint('Erreur PDF: $error');
        },
        onPageError: (page, error) {
          debugPrint('Erreur page PDF $page: $error');
        },
      ),
    );
  }

  Widget _buildDeviceStatusSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Device status',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        Row(
          children: [
            Text(
              _isConnected ? 'Device connected' : 'Device not connected',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(width: 8),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[600]),
          ],
        ),
      ],
    );
  }

  Widget _buildPrintDirectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Print direction',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildRotationOption(0, '0°'),
            _buildRotationOption(90, '90°'),
            _buildRotationOption(180, '180°'),
            _buildRotationOption(270, '270°'),
          ],
        ),
      ],
    );
  }

  Widget _buildRotationOption(int rotation, String label) {
    final isSelected = _selectedRotation == rotation;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRotation = rotation;
        });
        _updateConfigFromUI();
      },
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Colors.blue[50] : Colors.white,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Transform.rotate(
              angle: rotation * 3.14159 / 180,
              child: Icon(
                Icons.phone_android,
                color: isSelected ? Colors.blue : Colors.grey[600],
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: isSelected ? Colors.blue : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMirrorSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Mirror',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMirrorOption(0, Icons.crop_original, 'Normal'),
            _buildMirrorOption(1, Icons.flip, 'Horizontal'),
            _buildMirrorOption(2, Icons.flip_camera_android, 'Vertical'),
          ],
        ),
      ],
    );
  }

  Widget _buildMirrorOption(int mirrorType, IconData icon, String label) {
    final isSelected = _selectedMirror == mirrorType;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedMirror = mirrorType;
        });
        _updateConfigFromUI();
      },
      child: Container(
        width: 80,
        height: 60,
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Colors.blue[50] : Colors.white,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.blue : Colors.grey[600],
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: isSelected ? Colors.blue : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrintCopiesSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Number of print copies',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        Row(
          children: [
            IconButton(
              onPressed:
                  _printCopies > 1
                      ? () {
                        setState(() {
                          _printCopies--;
                        });
                        _updateConfigFromUI();
                      }
                      : null,
              icon: const Icon(Icons.remove),
              style: IconButton.styleFrom(
                backgroundColor: Colors.grey[200],
                foregroundColor: Colors.black,
              ),
            ),
            Container(
              width: 40,
              height: 40,
              alignment: Alignment.center,
              child: Text(
                '$_printCopies',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            IconButton(
              onPressed: () {
                setState(() {
                  _printCopies++;
                });
                _updateConfigFromUI();
              },
              icon: const Icon(Icons.add),
              style: IconButton.styleFrom(
                backgroundColor: Colors.grey[200],
                foregroundColor: Colors.black,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _printTicket() async {
    // Vérifier la connexion avant d'imprimer
    await _checkConnectionStatus();

    if (!_isConnected) {
      _showErrorDialog('Imprimante non connectée. Veuillez vous reconnecter.');
      return;
    }

    // Test de connexion rapide
    try {
      bool connectionTest = await _printerService.testConnection();
      if (!connectionTest) {
        _showErrorDialog(
          'Test de connexion échoué. Veuillez vérifier l\'imprimante.',
        );
        return;
      }
    } catch (e) {
      _showErrorDialog('Erreur de test de connexion: $e');
      return;
    }

    setState(() {
      _isPrinting = true;
      _errorMessage = null;
    });

    try {
      Uint8List bytes;

      if (widget.invoice != null) {
        // Créer les données de reçu à partir de la facture
        final receiptData = ReceiptData(
          companyName: 'HCP DESIGN',
          companyAddress: 'Abidjan, Côte d\'Ivoire',
          companyPhone: '+225 07 09 49 58 48',
          companyEmail: '<EMAIL>',
          companyWebsite: 'www.hcp-designci.com',
          title: 'MINI FACTURE',
          referenceNumber: 'F${widget.invoice!.id.toString().padLeft(6, '0')}',
          date: DateTime.now(),
          clientName: widget.invoice!.clientName,
          clientNumber: widget.invoice!.clientNumber,
          deliveryLocation: widget.invoice!.deliveryLocation,
          deliveryDetails: widget.invoice!.deliveryDetails,
          total: widget.invoice!.total,
          qrCodes: [
            QRCodeData(label: 'Site Web', data: 'https://www.hcp-designci.com'),
            QRCodeData(label: 'WhatsApp', data: 'https://wa.me/2250709495848'),
          ],
          footerText: 'Merci pour votre confiance - HCP DESIGN',
        );

        // Générer les bytes pour la mini facture
        bytes = await ReceiptBuilder.generateReceipt(receiptData, _config);
      } else if (widget.pdfPath != null) {
        // Convertir le PDF en bytes d'impression thermique
        bytes = await _printerService.convertPdfToThermalBytes(widget.pdfPath!);
      } else {
        // Générer un reçu de test
        bytes = await ReceiptBuilder.generateTestReceipt();
      }

      // Imprimer avec le nombre de copies configuré
      final copiesToPrint = _config.copies;
      debugPrint('🖨️ Impression de $copiesToPrint copie(s)');

      for (int i = 0; i < copiesToPrint; i++) {
        bool printSuccess = await _printerService.printBytes(bytes);
        if (!printSuccess) {
          throw Exception('Échec de l\'impression (copie ${i + 1})');
        }

        // Petite pause entre les copies
        if (i < copiesToPrint - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      debugPrint('❌ Erreur impression: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Erreur: $e';
        });
        _showErrorDialog(_errorMessage!);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPrinting = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('✅ Succès'),
            content: Text(
              'Mini facture imprimée avec succès (${_config.copies} copie${_config.copies > 1 ? 's' : ''})',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop(); // Retour à la liste des factures
                },
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('❌ Erreur'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}
