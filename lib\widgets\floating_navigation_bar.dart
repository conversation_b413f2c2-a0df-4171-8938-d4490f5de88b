import 'package:flutter/material.dart';

class FloatingNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const FloatingNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(35),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(
            iconPath: 'assets/icons/navigation/dashboard.png',
            index: 0,
            isCenter: false,
          ),
          _buildNavItem(
            iconPath: 'assets/icons/navigation/receipt.png',
            index: 1,
            isCenter: false,
          ),
          _buildNavItem(
            iconPath: 'assets/icons/navigation/inventory.png',
            index: 2,
            isCenter: false,
          ),
          _buildNavItem(
            iconPath: 'assets/icons/navigation/tasks.png',
            index: 3,
            isCenter: false,
          ),
          _buildNavItem(
            iconPath: 'assets/icons/navigation/whatsapp.png',
            index: 4,
            isCenter: false,
            color: const Color(0xFF25D366), // Couleur WhatsApp
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String iconPath,
    required int index,
    required bool isCenter,
    Color? color,
  }) {
    final bool isSelected = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Colors.white.withValues(alpha: 0.3)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Padding(
          padding: const EdgeInsets.all(
            11.0,
          ), // Padding ajusté pour les icônes plus grandes (28px)
          child: _buildIconWidget(iconPath, index, isSelected, color),
        ),
      ),
    );
  }

  Widget _buildIconWidget(
    String iconPath,
    int index,
    bool isSelected,
    Color? color,
  ) {
    // Utilisation exclusive de vos icônes PNG
    return Image.asset(
      iconPath,
      width: 28,
      height: 28,
      fit: BoxFit.contain,
      // Suppression temporaire du colorBlendMode pour tester vos PNG
      filterQuality: FilterQuality.high,
      errorBuilder: (context, error, stackTrace) {
        // En cas d'erreur, afficher un container coloré au lieu d'une icône Material
        return Container(
          width: 28,
          height: 28,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              '${index + 1}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black26,
                    offset: Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
