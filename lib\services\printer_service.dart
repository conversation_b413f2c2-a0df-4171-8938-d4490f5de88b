import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:permission_handler/permission_handler.dart';

/// Modèle pour représenter une imprimante découverte
class DiscoveredPrinter {
  final String id;
  final String name;
  final String address;
  final PrinterType type;
  final bool isConnected;
  final int? rssi; // Signal strength for Bluetooth

  DiscoveredPrinter({
    String? id,
    required this.name,
    required this.address,
    required this.type,
    this.isConnected = false,
    this.rssi,
  }) : id = id ?? address; // Use address as default id

  // Factory constructor from BluetoothInfo
  factory DiscoveredPrinter.fromBluetoothInfo(BluetoothInfo info) {
    return DiscoveredPrinter(
      id: info.macAdress,
      name: info.name,
      address: info.macAdress,
      type: PrinterType.bluetooth,
    );
  }

  /// Crée une copie de cette instance avec les valeurs modifiées
  DiscoveredPrinter copyWith({
    String? id,
    String? name,
    String? address,
    PrinterType? type,
    bool? isConnected,
    int? rssi,
  }) {
    return DiscoveredPrinter(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      type: type ?? this.type,
      isConnected: isConnected ?? this.isConnected,
      rssi: rssi ?? this.rssi,
    );
  }

  @override
  String toString() {
    return 'DiscoveredPrinter(id: $id, name: $name, address: $address, type: $type, connected: $isConnected)';
  }
}

/// Types d'imprimantes supportées
enum PrinterType {
  bluetooth,
  wifi,
  usb,
}

/// États de connexion de l'imprimante
enum PrinterConnectionStatus {
  disconnected,
  connecting,
  connected,
  error,
}

/// Service principal pour la gestion des imprimantes thermiques ESC/POS
class PrinterService {
  static final PrinterService _instance = PrinterService._internal();
  factory PrinterService() => _instance;
  PrinterService._internal();
  
  // État du service
  PrinterConnectionStatus _connectionStatus = PrinterConnectionStatus.disconnected;
  DiscoveredPrinter? _connectedPrinter;
  final List<DiscoveredPrinter> _discoveredPrinters = [];
  
  // Streams pour notifier les changements d'état
  final StreamController<PrinterConnectionStatus> _statusController = 
      StreamController<PrinterConnectionStatus>.broadcast();
  final StreamController<List<DiscoveredPrinter>> _printersController = 
      StreamController<List<DiscoveredPrinter>>.broadcast();

  // Getters publics
  PrinterConnectionStatus get connectionStatus => _connectionStatus;
  DiscoveredPrinter? get connectedPrinter => _connectedPrinter;
  List<DiscoveredPrinter> get discoveredPrinters => List.unmodifiable(_discoveredPrinters);
  
  // Streams publics
  Stream<PrinterConnectionStatus> get statusStream => _statusController.stream;
  Stream<List<DiscoveredPrinter>> get printersStream => _printersController.stream;

  /// Initialise le service et vérifie les permissions
  Future<bool> initialize() async {
    try {
      debugPrint('🖨️ Initialisation du PrinterService...');
      
      // Vérifier et demander les permissions Bluetooth
      final hasPermissions = await _checkAndRequestPermissions();
      if (!hasPermissions) {
        debugPrint('❌ Permissions Bluetooth refusées');
        return false;
      }

      // Initialiser Bluetooth
      await _initializeBluetooth();
      
      debugPrint('✅ PrinterService initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'initialisation du PrinterService: $e');
      return false;
    }
  }

  /// Vérifie et demande les permissions nécessaires
  Future<bool> _checkAndRequestPermissions() async {
    try {
      // Permissions pour Android 12+
      final permissions = [
        Permission.bluetooth,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.bluetoothAdvertise,
        Permission.location,
      ];

      Map<Permission, PermissionStatus> statuses = await permissions.request();
      
      // Vérifier si toutes les permissions sont accordées
      bool allGranted = true;
      for (var permission in permissions) {
        final status = statuses[permission];
        if (status != PermissionStatus.granted) {
          debugPrint('❌ Permission refusée: $permission - Status: $status');
          allGranted = false;
        }
      }

      return allGranted;
    } catch (e) {
      debugPrint('❌ Erreur lors de la vérification des permissions: $e');
      return false;
    }
  }

  /// Initialise le Bluetooth
  Future<void> _initializeBluetooth() async {
    try {
      // Vérifier si Bluetooth est activé
      bool isEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      if (!isEnabled) {
        debugPrint('❌ Bluetooth non activé');
        throw Exception('Bluetooth non activé');
      }

      debugPrint('✅ Bluetooth initialisé');
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'initialisation Bluetooth: $e');
      rethrow;
    }
  }

  /// Scanne les imprimantes disponibles
  Future<List<DiscoveredPrinter>> scan({PrinterType? type}) async {
    try {
      debugPrint('🔍 Début du scan des imprimantes...');
      _updateConnectionStatus(PrinterConnectionStatus.connecting);
      
      _discoveredPrinters.clear();
      
      // Scanner les imprimantes Bluetooth si demandé ou par défaut
      if (type == null || type == PrinterType.bluetooth) {
        await _scanBluetoothPrinters();
      }
      
      // Scanner les imprimantes WiFi si demandé
      if (type == null || type == PrinterType.wifi) {
        await _scanWifiPrinters();
      }
      
      _updateConnectionStatus(PrinterConnectionStatus.disconnected);
      _printersController.add(List.from(_discoveredPrinters));
      
      debugPrint('✅ Scan terminé: ${_discoveredPrinters.length} imprimantes trouvées');
      return List.from(_discoveredPrinters);
    } catch (e) {
      debugPrint('❌ Erreur lors du scan: $e');
      _updateConnectionStatus(PrinterConnectionStatus.error);
      return [];
    }
  }

  /// Scanne les imprimantes Bluetooth
  Future<void> _scanBluetoothPrinters() async {
    try {
      debugPrint('🔍 Scan Bluetooth en cours...');
      
      // Vérifier si Bluetooth est activé
      bool isBluetoothEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      if (!isBluetoothEnabled) {
        debugPrint('❌ Bluetooth non activé');
        return;
      }
      
      // Obtenir les appareils Bluetooth appairés
      List<BluetoothInfo> pairedDevices = await PrintBluetoothThermal.pairedBluetooths;
      
      for (BluetoothInfo device in pairedDevices) {
        final printer = DiscoveredPrinter.fromBluetoothInfo(device);
        
        // Éviter les doublons
        if (!_discoveredPrinters.any((p) => p.address == printer.address)) {
          _discoveredPrinters.add(printer);
          debugPrint('📱 Imprimante Bluetooth trouvée: ${printer.name} (${printer.address})');
        }
      }
      
      debugPrint('✅ Scan Bluetooth terminé');
    } catch (e) {
      debugPrint('❌ Erreur scan Bluetooth: $e');
    }
  }

  /// Scanne les imprimantes WiFi/TCP
  Future<void> _scanWifiPrinters() async {
    try {
      debugPrint('🔍 Scan WiFi en cours...');
      
      // Pour les imprimantes WiFi, on peut scanner le réseau local
      // ou permettre à l'utilisateur d'entrer manuellement l'IP
      // Ici, on ajoute quelques exemples d'IPs communes
      final commonIPs = [
        '*************',
        '*************',
        '*************',
        '*************',
      ];
      
      for (String ip in commonIPs) {
        // Test de connexion rapide pour voir si une imprimante répond
        try {
          // Simuler un test de connexion (à implémenter selon les besoins)
          final printer = DiscoveredPrinter(
            name: 'Imprimante WiFi ($ip)',
            address: '$ip:9100', // Port standard ESC/POS
            type: PrinterType.wifi,
            isConnected: false,
          );
          
          // Pour l'instant, on ajoute toutes les IPs comme potentielles
          // Dans une vraie implémentation, on testerait la connexion
          _discoveredPrinters.add(printer);
          debugPrint('🌐 Imprimante WiFi potentielle: $ip');
        } catch (e) {
          // Ignorer les erreurs de connexion pour le scan
        }
      }
      
      debugPrint('✅ Scan WiFi terminé');
    } catch (e) {
      debugPrint('❌ Erreur scan WiFi: $e');
    }
  }

  /// Se connecte à une imprimante
  Future<bool> connect(DiscoveredPrinter printer) async {
    try {
      debugPrint('🔗 Connexion à l\'imprimante: ${printer.name}');
      _updateConnectionStatus(PrinterConnectionStatus.connecting);
      
      bool success = false;
      
      switch (printer.type) {
        case PrinterType.bluetooth:
          success = await _connectBluetooth(printer);
          break;
        case PrinterType.wifi:
          success = await _connectWifi(printer);
          break;
        case PrinterType.usb:
          success = await _connectUsb(printer);
          break;
      }
      
      if (success) {
        _connectedPrinter = printer;
        _updateConnectionStatus(PrinterConnectionStatus.connected);
        debugPrint('✅ Connexion réussie à ${printer.name}');
      } else {
        _updateConnectionStatus(PrinterConnectionStatus.error);
        debugPrint('❌ Échec de connexion à ${printer.name}');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ Erreur lors de la connexion: $e');
      _updateConnectionStatus(PrinterConnectionStatus.error);
      return false;
    }
  }

  /// Connexion Bluetooth
  Future<bool> _connectBluetooth(DiscoveredPrinter printer) async {
    try {
      debugPrint('🔗 Tentative de connexion Bluetooth à: ${printer.name} (${printer.address})');
      
      // Vérifier d'abord si Bluetooth est activé
      bool isBluetoothEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      if (!isBluetoothEnabled) {
        debugPrint('❌ Bluetooth désactivé');
        throw Exception('Bluetooth désactivé. Veuillez l\'activer.');
      }
      
      // Tentative de connexion avec timeout
      bool connected = false;
      int attempts = 0;
      const maxAttempts = 3;
      
      while (!connected && attempts < maxAttempts) {
        attempts++;
        debugPrint('🔄 Tentative $attempts/$maxAttempts pour ${printer.name}');
        
        try {
          connected = await PrintBluetoothThermal.connect(
            macPrinterAddress: printer.address,
          ).timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              debugPrint('⏰ Timeout de connexion pour ${printer.name}');
              return false;
            },
          );
          
          if (connected) {
            debugPrint('✅ Connexion réussie à ${printer.name} (tentative $attempts)');
            
            // Test de communication
            await Future.delayed(const Duration(milliseconds: 500));
            bool isReady = await _testPrinterConnection();
            
            if (isReady) {
              debugPrint('✅ Imprimante prête: ${printer.name}');
              return true;
            } else {
              debugPrint('⚠️ Imprimante connectée mais non prête: ${printer.name}');
              await PrintBluetoothThermal.disconnect;
              connected = false;
            }
          } else {
            debugPrint('❌ Échec connexion tentative $attempts: ${printer.name}');
            if (attempts < maxAttempts) {
              await Future.delayed(const Duration(seconds: 2));
            }
          }
        } catch (e) {
          debugPrint('❌ Erreur tentative $attempts: $e');
          if (attempts < maxAttempts) {
            await Future.delayed(const Duration(seconds: 2));
          }
        }
      }
      
      if (!connected) {
        throw Exception('Impossible de se connecter à ${printer.name} après $maxAttempts tentatives');
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ Erreur connexion Bluetooth: $e');
      return false;
    }
  }
  
  /// Test la connexion avec l'imprimante
  Future<bool> _testPrinterConnection() async {
    try {
      // Envoyer une commande de test simple (initialisation ESC/POS)
      final testCommand = Uint8List.fromList([0x1B, 0x40]); // ESC @ (Initialize)
      await PrintBluetoothThermal.writeBytes(testCommand);
      
      // Attendre un peu pour la réponse
      await Future.delayed(const Duration(milliseconds: 500));
      
      return true;
    } catch (e) {
      debugPrint('❌ Test de connexion échoué: $e');
      return false;
    }
  }

  /// Test rapide de connectivité sans établir une connexion complète
  Future<bool> testPrinterConnectivity(DiscoveredPrinter printer) async {
    try {
      if (printer.type == PrinterType.bluetooth) {
        // Pour Bluetooth, vérifier si l'appareil est appairé et disponible
        List<BluetoothInfo> pairedDevices = await PrintBluetoothThermal.pairedBluetooths;
        bool isPaired = pairedDevices.any((device) => 
          device.macAdress == printer.address || device.name == printer.name);
        return isPaired;
      } else if (printer.type == PrinterType.wifi) {
        // Pour WiFi, faire un ping rapide
        return await _pingWifiPrinter(printer.address);
      }
      return false;
    } catch (e) {
      debugPrint('❌ Test de connectivité échoué pour ${printer.name}: $e');
      return false;
    }
  }

  Future<bool> _pingWifiPrinter(String address) async {
    try {
      // Simuler un test de connectivité WiFi
      // Dans une vraie implémentation, on pourrait utiliser un socket ou HTTP request
      await Future.delayed(const Duration(milliseconds: 100));
      return true; // Simuler que l'imprimante répond
    } catch (e) {
      return false;
    }
  }

  /// Connexion WiFi/TCP
  Future<bool> _connectWifi(DiscoveredPrinter printer) async {
    try {
      // WiFi connection logic can be implemented later
      // For now, return false as WiFi is not implemented
      debugPrint('⚠️ WiFi connection not implemented yet');
      return false;
    } catch (e) {
      debugPrint('❌ Erreur connexion WiFi: $e');
      return false;
    }
  }

  /// Connexion USB (pour les appareils supportés)
  Future<bool> _connectUsb(DiscoveredPrinter printer) async {
    try {
      // Implémentation USB si nécessaire
      debugPrint('⚠️ Connexion USB non implémentée');
      return false;
    } catch (e) {
      debugPrint('❌ Erreur connexion USB: $e');
      return false;
    }
  }

  /// Se déconnecte de l'imprimante actuelle
  Future<bool> disconnect() async {
    try {
      if (_connectedPrinter == null) {
        debugPrint('⚠️ Aucune imprimante connectée');
        return true;
      }
      
      debugPrint('🔌 Déconnexion de ${_connectedPrinter!.name}');
      
      bool success = false;
      
      switch (_connectedPrinter!.type) {
        case PrinterType.bluetooth:
          await PrintBluetoothThermal.disconnect;
          success = true;
          break;
        case PrinterType.wifi:
            // WiFi disconnect logic can be implemented later
            success = true;
            break;
        case PrinterType.usb:
          success = true;
          break;
      }
      
      if (success) {
        _connectedPrinter = null;
        _updateConnectionStatus(PrinterConnectionStatus.disconnected);
        debugPrint('✅ Déconnexion réussie');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ Erreur lors de la déconnexion: $e');
      return false;
    }
  }

  /// Imprime des données brutes (bytes)
  Future<bool> printBytes(Uint8List bytes) async {
    try {
      if (_connectedPrinter == null) {
        debugPrint('❌ Aucune imprimante connectée');
        return false;
      }
      
      if (_connectionStatus != PrinterConnectionStatus.connected) {
        debugPrint('❌ Imprimante non connectée');
        return false;
      }
      
      debugPrint('🖨️ Impression en cours... (${bytes.length} bytes)');
      
      bool success = false;
      
      switch (_connectedPrinter!.type) {
        case PrinterType.bluetooth:
          await PrintBluetoothThermal.writeBytes(bytes);
          success = true;
          break;
        case PrinterType.wifi:
          // WiFi printing logic can be implemented later
          success = true;
          break;
        case PrinterType.usb:
          debugPrint('⚠️ Impression USB non implémentée');
          success = false;
          break;
      }
      
      if (success) {
        debugPrint('✅ Impression réussie');
      } else {
        debugPrint('❌ Échec de l\'impression');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'impression: $e');
      return false;
    }
  }

  /// Met à jour le statut de connexion
  void _updateConnectionStatus(PrinterConnectionStatus status) {
    _connectionStatus = status;
    _statusController.add(status);
  }

  /// Teste la connexion avec l'imprimante
  Future<bool> testConnection() async {
    if (_connectedPrinter == null) return false;
    
    try {
      // Envoyer une commande de test simple
      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm58, profile);
      final bytes = generator.feed(1);
      
      return await printBytes(Uint8List.fromList(bytes));
    } catch (e) {
      debugPrint('❌ Erreur test connexion: $e');
      return false;
    }
  }

  /// Scanner les imprimantes Bluetooth disponibles
  Future<List<DiscoveredPrinter>> scanBluetoothPrinters() async {
    await _scanBluetoothPrinters();
    _printersController.add(List.from(_discoveredPrinters));
    return _discoveredPrinters.where((p) => p.type == PrinterType.bluetooth).toList();
  }

  /// Scanner les imprimantes WiFi disponibles
  Future<List<DiscoveredPrinter>> scanWifiPrinters() async {
    await _scanWifiPrinters();
    _printersController.add(List.from(_discoveredPrinters));
    return _discoveredPrinters.where((p) => p.type == PrinterType.wifi).toList();
  }

  /// Méthode pour scanner toutes les imprimantes (alias pour compatibilité)
  Future<List<DiscoveredPrinter>> scanForPrinters() async {
    _discoveredPrinters.clear();
    await _scanBluetoothPrinters();
    await _scanWifiPrinters();
    _printersController.add(List.from(_discoveredPrinters));
    return _discoveredPrinters;
  }

  /// Nettoie les ressources
  void dispose() {
    _statusController.close();
    _printersController.close();
    disconnect();
  }
}