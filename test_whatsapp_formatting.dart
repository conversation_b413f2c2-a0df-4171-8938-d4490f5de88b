/// Test du formatage des numéros de téléphone pour WhatsApp
///
/// Ce fichier teste la logique de formatage des numéros pour s'assurer
/// que l'indicatif +225 est correctement ajouté selon les cas.

void main() {
  print('🧪 Test du formatage des numéros WhatsApp\n');

  // Cas de test
  final testCases = [
    // [numéro d'entrée, résultat attendu, description]
    ['07 09 49 58 48', '+225709495848', 'Numéro avec espaces commençant par 0'],
    ['0709495848', '+225709495848', 'Numéro sans espaces commençant par 0'],
    ['+225 07 09 49 58 48', '+2250709495848', 'Numéro avec +225 et espaces'],
    ['+2250709495848', '+2250709495848', 'Numéro avec +225 sans espaces'],
    ['225 07 09 49 58 48', '+2250709495848', 'Numéro avec 225 et espaces'],
    ['2250709495848', '+2250709495848', 'Numéro avec 225 sans espaces'],
    ['709495848', '+225709495848', 'Numéro sans indicatif ni 0'],
    ['', '', 'Numéro vide'],
    ['07-09-49-58-48', '+225709495848', 'Numéro avec tirets'],
    ['***********.48', '+225709495848', 'Numéro avec points'],
  ];

  int passed = 0;
  int failed = 0;

  for (final testCase in testCases) {
    final input = testCase[0];
    final expected = testCase[1];
    final description = testCase[2];

    final result = formatPhoneForWhatsApp(input);

    if (result == expected) {
      print('✅ PASS: $description');
      print('   Input: "$input" → Output: "$result"');
      passed++;
    } else {
      print('❌ FAIL: $description');
      print('   Input: "$input"');
      print('   Expected: "$expected"');
      print('   Got: "$result"');
      failed++;
    }
    print('');
  }

  print('📊 Résultats des tests:');
  print('   ✅ Réussis: $passed');
  print('   ❌ Échoués: $failed');
  print(
    '   📈 Taux de réussite: ${(passed / (passed + failed) * 100).toStringAsFixed(1)}%',
  );
}

/// Formate le numéro de téléphone pour WhatsApp
/// (Copie de la méthode de InvoiceListPage pour les tests)
String formatPhoneForWhatsApp(String? phoneNumber) {
  if (phoneNumber == null || phoneNumber.isEmpty) {
    return '';
  }

  // Nettoyer le numéro (supprimer espaces, tirets, etc.)
  String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

  // Si le numéro commence déjà par +225, le retourner tel quel
  if (cleanNumber.startsWith('+225')) {
    return cleanNumber;
  }

  // Si le numéro commence par 225 sans +, ajouter le +
  if (cleanNumber.startsWith('225')) {
    return '+$cleanNumber';
  }

  // Si le numéro ne commence pas par l'indicatif, l'ajouter
  if (cleanNumber.startsWith('0')) {
    // Supprimer le 0 initial et ajouter +225
    return '+225${cleanNumber.substring(1)}';
  }

  // Pour les autres cas, ajouter +225 directement
  return '+225$cleanNumber';
}
